
import jsonlines
import json
import os
from datasets import load_dataset
from colorama import Fore, Style

def load_dataset_from_path(split="train", dataset_path="./datasets", stream_mode=False):

    # loading train dataset
    # list all files undeer the dataset path
    dataset_files = os.listdir(dataset_path)
    print(Fore.GREEN + f"Loading {split} dataset..." + Fore.RESET)
    print("0. Return.")
    # print filelist with index
    for i in range(len(dataset_files)):
        print(f"{i+1}. {dataset_files[i]}")
    print("Choice:", end=" ")
    option = input()
    match option:
        case "0":
            print(f"{split} dataset is empty...")
        case _:
            train_dataset_name = dataset_files[int(option)-1]
            print(f"Loading {split} dataset: {train_dataset_name}")

    data_file_path = dataset_path+"/"+train_dataset_name
    row_count = 0
    with open(data_file_path, "r") as file:
        row_count = sum(1 for line in file)

    train_dataset = load_dataset("json", data_files=data_file_path, split="train", streaming=stream_mode)

    return row_count, train_dataset


def input_generate_dataset_path(tip_info):

    print(tip_info)
    print("0. Return.")
    print("1. Samples conversations.")
    print("2. Samples sentence withe token level labels for classification.")
    print("3. train classification sentences.")
    print("Choice:", end=" ")

    dataset_type = ""
    dataset_name = ""
    option = input()
    match option:
        case "0":
            print("Exiting...")
        case "1":
            dataset_type = "sample conversation"
            dataset_name = "sample_dataset_conversationclassification"
        case "2":
            dataset_type = "sample classification"
            dataset_name = "sample_dataset_tokenclassification"
        case "3":
            dataset_type = "train classification"
            dataset_name = "train_dataset_tokenclassification"
        case _:
            print("Invalid option. Returning...")

    return dataset_type, dataset_name

def generate_dataset(dataset_type, dataset_name, dataset_path="./datasets"):

    if os.path.exists(dataset_path) == False:
        os.makedirs(dataset_path)

    match dataset_type:
        case "sample conversation":
            generate_sample_conversationclassification_dataset(dataset_name, dataset_path)
        case "sample classification":
            generate_sample_tokenclassification_dataset(dataset_name, dataset_path)
        case "train classification":
            generate_train_tokenclassification_dataset(dataset_name, dataset_path)
        case _:
            print("Invalid dataset type. Exiting...")

def generate_sample_conversationclassification_dataset(dataset_name, dataset_path="./datasets"):

    print("Generating sample conversation dataset...")

    sample_questions = [
        "编号为***********的项目经理?",
        "XT-20250102的设计师是谁",
        "号码XT-20250103的测试人员是哪个",
        "明天什么天气？",
        "你吃了没有？"
    ]

    sample_intent_labels = [
        1,
        1,
        1,
        0,
        0
    ]

    sample_token_labels = [
        ["O","O","O","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-ROLE-MGR","I-ROLE-MGR","I-ROLE-MGR","I-ROLE-MGR","O"],
        ["B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-ROLE-DES","I-ROLE-DES","I-ROLE-DES","O","O"], 
        ["O","O","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-ROLE-TST","I-ROLE-TST","I-ROLE-TST","I-ROLE-TST","I-ROLE-TST","O","O","O"],
        ["O","O","O","O","O","O","O"],
        ["O","O","O","O","O","O"]
    ]

    chat_data = []
    #chat_data.extend([system_message])
    for i in range(len(sample_questions)):
        chat_data.extend(
            [
                {
                    "conversation": {
                        "content": sample_questions[i],
                        "token_labels": sample_token_labels[i],
                        "intent_labels": sample_intent_labels[i]
                    }

                }
            ]
        )
    
    with jsonlines.open(dataset_path+"/"+dataset_name+".json", "w") as writer:
        writer.write_all(chat_data)
    

from transformers import AutoTokenizer, RobertaTokenizer
def generate_sample_tokenclassification_dataset(dataset_name, dataset_path="./datasets", tokenizer_path="./dl_models/hfl/chinese-roberta-wwm-ext"):

    sentence_data = []

    sentences = [
    "项目***********-0121的负责人是谁",
    "谁是项目***********-0122的负责人",
    "谁管理项目***********-0123",
    "编号***********-0124的项目谁在管",
    "谁负责***********-0124项目"
    ]
    tokenize_sentences = [
        ["项", "目", "x", "##t", "-", "2025", "##010", "##1", "-", "012", "##1", "的", "负", "责", "人", "是", "谁"],
        ["谁", "是", "项", "目", "x", "##t", "-", "2025", "##010", "##1", "-", "012", "##2", "的", "负", "责", "人"],
        ["谁", "管", "理", "项", "目", "x", "##t", "-", "2025", "##010", "##1", "-", "012", "##3"],
        ["编", "号", "x", "##t", "-", "2025", "##010", "##1", "-", "012", "##4", "的", "项", "目", "谁", "在", "管"],
        ["谁", "负", "责", "x", "##t", "-", "2025", "##010", "##1", "-", "012", "##4", "项", "目"]
    ]

    label_sentences = [
        ["B-PROJ","I-PROJ","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-PROJMGR","I-PROJMGR","I-PROJMGR","O","O"],
        ["O","O","B-PROJ","I-PROJ","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-PROJMGR","I-PROJMGR","I-PROJMGR"],
        ["O","B-PROJMGR","I-PROJMGR","B-PROJ","I-PROJ","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID"],
        ["O","O","B-PROJID","I-PROJID","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","O","B-PROJ","I-PROJ","O","O","B-PROJMGR"],
        ["O","B-PROJMGR","I-PROJMGR","B-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","I-PROJID","B-PROJ","I-PROJ"]
    ]

    label_names = [
        "B-PROJ",
        "B-PROJID",
        "B-PROJMGR"
    ]

    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    for i in range(len(sentences)):
        tokens = tokenizer.tokenize(sentences[i])
        tokenize_sentences.append(tokens)
        # align labels with tokenized input
        # must be same length, or will raise error in train calc loss
        assert(len(tokens) == len(label_sentences[i])), f"row-{i} Tokenized sentence-{len(tokens)} and labels-{len(label_sentences[i])} must have the same length."
        sentence_data.extend(
            [
                {
                    "sentence": {
                        "content": sentences[i],
                        "tokens": tokens,
                        "labels": label_sentences[i]
                    }
                }
            ]
        )

    with jsonlines.open(dataset_path+"/"+dataset_name+".json", "w") as writer:
        writer.write_all(sentence_data)

def generate_train_tokenclassification_dataset(dataset_name, dataset_path="./datasets", tokenizer_path="./dl_models/hfl/chinese-roberta-wwm-ext"):

    # load sentence from gen_from_ai.txt
    with open(dataset_path + "/" + "ai_train_tokenclassification.txt", "r") as file:
        sentences = file.readlines()

    tokenize_sentences = []
    label_sentences = []
    sentence_data = []
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    for i in range(len(sentences)):
        tokens = tokenizer.tokenize(sentences[i])
        tokenize_sentences.append(tokens)
        # align labels with tokenized input
        # must be same length, or will raise error in train calc loss
        #assert(len(tokens) == len(label_sentences[i])), f"row-{i} Tokenized sentence-{len(tokens)} and labels-{len(label_sentences[i])} must have the same length."
        sentence_data.extend(
            [
                {
                    "sentence": {
                        "content": sentences[i],
                        "tokens": tokens,
                        "labels": ""
                    }
                }
            ]
        )

    with jsonlines.open(dataset_path+"/"+dataset_name+".json", "w") as writer:
        writer.write_all(sentence_data)
