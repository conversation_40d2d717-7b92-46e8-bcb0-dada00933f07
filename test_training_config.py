#!/usr/bin/env python3
"""
测试修复后的双任务模型训练配置
对比原始模型和双任务模型的训练参数
"""

import json
import os
from my_roberta_entity_relation import train_entity_relation_model, load_labels_fromfile

def test_training_configuration():
    """
    测试训练配置，但不实际运行训练
    """
    print("🔧 测试双任务模型训练配置")
    print("=" * 60)
    
    # 1. 检查数据集
    dataset_path = "datasets/my_roberta_v2_traindata_entity_relation_only0724.json"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集文件不存在: {dataset_path}")
        return False
    
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    print(f"📊 数据集信息:")
    print(f"  • 总样本数: {len(dataset)}")
    
    # 2. 检查标签文件
    try:
        entity_id_map, relation_id_map = load_labels_fromfile()
        print(f"  • 实体标签数: {len(entity_id_map)}")
        print(f"  • 关系标签数: {len(relation_id_map)}")
    except Exception as e:
        print(f"❌ 标签文件加载失败: {e}")
        return False
    
    # 3. 计算训练配置
    train_split_ratio = 0.8
    train_size = int(len(dataset) * train_split_ratio)
    eval_size = len(dataset) - train_size
    
    # 训练参数（与修复后的配置一致）
    per_device_train_batch_size = 1
    gradient_accumulation_steps = 4
    num_train_epochs = 5
    
    effective_batch_size = per_device_train_batch_size * gradient_accumulation_steps
    steps_per_epoch = train_size // effective_batch_size
    total_steps = steps_per_epoch * num_train_epochs
    
    # 动态计算评估和保存频率
    save_steps = max(100, steps_per_epoch // 2)
    eval_steps = max(50, steps_per_epoch // 4)
    
    print(f"\n📈 训练配置对比:")
    print(f"  原始四任务模型配置:")
    print(f"    • 批次大小: 1-4")
    print(f"    • 梯度累积: 4-8")
    print(f"    • 有效批次: 4-32")
    print(f"    • 训练时长: 16+ 分钟")
    print(f"    • 评估频率: 动态计算")
    
    print(f"\n  修复后双任务模型配置:")
    print(f"    • 批次大小: {per_device_train_batch_size}")
    print(f"    • 梯度累积: {gradient_accumulation_steps}")
    print(f"    • 有效批次: {effective_batch_size}")
    print(f"    • 训练集大小: {train_size}")
    print(f"    • 验证集大小: {eval_size}")
    print(f"    • 每轮步数: {steps_per_epoch}")
    print(f"    • 总训练步数: {total_steps}")
    print(f"    • 评估频率: 每 {eval_steps} 步")
    print(f"    • 保存频率: 每 {save_steps} 步")
    print(f"    • 预计训练时长: {total_steps * 0.5 / 60:.1f} 分钟 (估算)")
    
    # 4. 对比分析
    print(f"\n🔍 配置分析:")
    
    # 原始模型的典型配置（基于日志推断）
    original_steps = 3465  # 从用户提供的信息
    original_time = 963    # 秒
    original_steps_per_second = original_steps / original_time
    
    predicted_time = total_steps / original_steps_per_second
    
    print(f"  • 原始模型: {original_steps} 步, {original_time} 秒")
    print(f"  • 双任务模型: {total_steps} 步, 预计 {predicted_time:.0f} 秒")
    print(f"  • 训练强度对比: {total_steps / original_steps:.2f}x")
    
    if total_steps < original_steps * 0.5:
        print(f"  ⚠️ 警告: 双任务模型的训练步数可能不足")
        print(f"  💡 建议: 增加训练轮数到 {int(num_train_epochs * 2)} 轮")
    elif total_steps > original_steps * 0.8:
        print(f"  ✅ 训练强度合适，应该有充分的训练")
    else:
        print(f"  ⚠️ 训练强度偏低，建议适当增加")
    
    # 5. 评估频率分析
    original_eval_frequency = original_steps // 10  # 假设原始模型每10%进度评估一次
    eval_frequency_ratio = eval_steps / original_eval_frequency if original_eval_frequency > 0 else 1
    
    print(f"\n📊 评估频率分析:")
    print(f"  • 原始模型评估频率: 约每 {original_eval_frequency} 步")
    print(f"  • 双任务模型评估频率: 每 {eval_steps} 步")
    print(f"  • 评估频率比: {eval_frequency_ratio:.2f}x")
    
    if eval_frequency_ratio > 2:
        print(f"  ✅ 评估更频繁，能更好地监控训练进度")
    elif eval_frequency_ratio < 0.5:
        print(f"  ⚠️ 评估频率偏低，可能错过最佳模型")
    else:
        print(f"  ✅ 评估频率合适")
    
    return True

def test_quick_training():
    """
    运行一个快速的训练测试（只训练几步）
    """
    print(f"\n🧪 快速训练测试")
    print("=" * 40)
    
    try:
        # 创建一个测试用的小数据集
        dataset_path = "datasets/my_roberta_v2_traindata_entity_relation_only0724.json"
        with open(dataset_path, 'r', encoding='utf-8') as f:
            full_dataset = json.load(f)
        
        # 只取前20个样本进行快速测试
        test_dataset = full_dataset[:20]
        test_dataset_path = "test_small_dataset.json"
        
        with open(test_dataset_path, 'w', encoding='utf-8') as f:
            json.dump(test_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试数据集: {len(test_dataset)} 个样本")
        
        # 运行快速训练（只训练1轮，很少的步数）
        print("🚀 开始快速训练测试...")
        
        model, tokenizer, output_dir = train_entity_relation_model(
            train_dataset_path=test_dataset_path,
            num_train_epochs=1,  # 只训练1轮
            per_device_train_batch_size=2,  # 稍大的批次以加快速度
            gradient_accumulation_steps=2,
            logging_steps=5,  # 更频繁的日志
            save_steps=10,    # 快速保存
            eval_steps=5,     # 快速评估
        )
        
        print(f"✅ 快速训练完成! 模型保存在: {output_dir}")
        
        # 清理测试文件
        os.remove(test_dataset_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 快速训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 双任务模型训练配置测试工具")
    print("=" * 60)
    
    # 1. 测试配置
    config_ok = test_training_configuration()
    
    if not config_ok:
        print("❌ 配置测试失败，请检查数据集和标签文件")
        return
    
    # 2. 询问是否运行快速训练测试
    print(f"\n❓ 是否运行快速训练测试？")
    print("   这将使用小数据集训练1轮来验证配置是否正确")
    choice = input("请输入 y/n: ").strip().lower()
    
    if choice == 'y':
        test_quick_training()
    else:
        print("⏭️ 跳过快速训练测试")
    
    print(f"\n🎯 测试完成!")
    print("💡 如果配置正确，现在可以运行完整的训练:")
    print("   python my_roberta_entity_relation.py")

if __name__ == "__main__":
    main()
