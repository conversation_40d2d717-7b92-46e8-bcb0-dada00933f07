# RoBERTa 预测 API - MVP版本

基于已训练的RoBERTa模型的文本分类和实体识别API服务，专为内部测试设计。

## 🎯 功能特性

- **语法分类**: query(查询)、judge(判断)、reason(原因分析)
- **子域分类**: baseinfo(基本信息)、delivery(发货)、contract(合同)、unknown(未知)
- **实体识别**: 识别86种实体类型（项目、人员、角色、系统等）
- **关系抽取**: 识别18种实体间关系
- **版本化API**: 支持 `/api/v1/predict` 端点
- **错误处理**: 结构化错误响应和详细调试信息

## 📁 文件结构

```
roberta_api/
├── main.py              # FastAPI应用主文件
├── model_loader.py      # 模型加载和推理服务
├── requirements.txt     # Python依赖
├── start.sh            # 启动脚本
├── test_api.py         # 测试脚本
└── README.md           # 本文档
```

## 🚀 快速开始

### 1. 启动API服务

```bash
# 进入项目根目录
cd /root/TrainQuestionIntentModel/roberta_api

# 使用指定的Python环境启动服务
/opt/conda/envs/wpk_modeltrain/bin/python roberta_api_main.py

# 或者使用启动脚本（如果环境变量已配置）
./start.sh
```

**启动成功标志**：
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
正在初始化模型服务...
模型加载成功: optimized_model_random_20250718_032139
模型服务初始化成功
INFO:     Application startup complete.
```

### 2. 验证服务状态

```bash
# 健康检查
curl http://127.0.0.1:8000/api/v1/health

# 预期响应
{
  "status": "healthy",
  "model_version": "optimized_model_random_20250718_032139",
  "api_version": "v1",
  "gpu_available": true,
  "error": null
}
```

## 📖 API文档

### 端点列表

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | API信息和文档链接 |
| `/api/v1/health` | GET | 健康检查 |
| `/api/v1/predict` | POST | 文本预测 |
| `/docs` | GET | Swagger UI 文档 |
| `/redoc` | GET | ReDoc 文档 |

### 预测请求格式

```json
{
  "text": "项目P-94871234的项目经理是谁？"
}
```
### 预测请求示例
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的物料清单为什么没有细化完成？"}' | jq
```

```cmd
curl.exe -X POST "http://172.17.6.100:9010/api/v1/predict" -H "Content-Type: application/json" -d "{\"text\": \"项目P-94871234的项目经理是谁？\"}" | jq
```
给外部的cmd窗口去使用的示例

**字段说明:**
- `text`: 输入文本，长度1-512字符，不能为空

### 预测响应格式

```json
{
  "success": true,
  "data": {
    "text": "项目P-94871234的项目经理是谁？",
    "grammar": {
      "predicted": ["query"],
      "confidence": 0.95
    },
    "subdomain": {
      "predicted": "baseinfo",
      "confidence": 0.88
    },
    "entities": [
      {
        "text": "P-94871234",
        "type": "Prj-Id",
        "start": 2,
        "end": 12,
        "confidence": 0.92
      }
    ],
    "relations": [
      {
        "entity1": {...},
        "entity2": {...},
        "relation": "prj:has_role",
        "confidence": 0.85
      }
    ]
  },
  "model_version": "optimized_model_random_20250718_032139",
  "api_version": "v1",
  "processing_time_ms": 45.2
}
```

### 错误响应格式

```json
{
  "success": false,
  "error_type": "validation_error",
  "error_message": "输入文本不能为空",
  "error_details": "输入数据验证失败",
  "api_version": "v1"
}
```

**错误类型:**
- `validation_error` (400): 输入数据验证失败
- `inference_error` (500): 模型推理过程错误
- `model_error` (503): 模型服务不可用
- `system_error` (500): 系统内部错误

## 🧪 测试

### 运行综合测试

```bash
# 运行所有测试
python3 test_api.py

# 只进行健康检查
python3 test_api.py --health

# 测试单个文本
python3 test_api.py --text "你的测试文本"

# 指定API地址
python3 test_api.py --url http://localhost:8080
```

### 使用现有测试数据

测试脚本会自动加载 `../datasets/my_roberta_v2_testdata.txt` 中的测试数据进行验证。

## 🔧 配置说明

### 环境要求

- Python 3.7+
- PyTorch 1.9+
- Transformers 4.20+
- FastAPI 0.104+

### 模型文件

API服务依赖以下文件：
- 模型: `../my_models/optimized_model_random_20250718_032139/`
- 标签: `../datasets/my_roberta_*_labels.json`
- 源码: `../my_roberta_conversation.py`

### 端口配置

默认端口: 8000
可通过启动脚本参数修改: `./start.sh 8080`

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件是否存在
   - 确认标签文件完整
   - 验证Python路径设置

2. **导入错误**
   - 确认在正确目录启动
   - 检查PYTHONPATH设置
   - 验证依赖包安装

3. **GPU相关错误**
   - 检查CUDA环境
   - 确认PyTorch GPU支持
   - 可在CPU模式下运行

### 调试模式

启动时添加详细日志：
```bash
python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --log-level debug
```

### 查看日志

API服务会在控制台输出详细的启动和运行日志，包括：
- 模型加载进度
- 请求处理信息
- 错误详情

## 📞 支持

如遇问题，请检查：
1. 控制台错误日志
2. 模型和数据文件完整性
3. Python环境和依赖包
4. 网络和端口占用情况

---

**版本**: v1.0.0  
**模型**: optimized_model_random_20250718_032139  
**更新时间**: 2025-01-23
