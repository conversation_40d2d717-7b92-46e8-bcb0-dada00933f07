#!/bin/bash

# RoBERTa API 启动脚本
# 用于在当前服务器环境中启动API服务

echo "=== RoBERTa API 启动脚本 ==="

# 设置工作目录
cd "$(dirname "$0")"
API_DIR=$(pwd)
PROJECT_ROOT=$(dirname "$API_DIR")

echo "API目录: $API_DIR"
echo "项目根目录: $PROJECT_ROOT"

# 设置Python路径，确保能导入父目录的模块
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 检查Python环境
echo "检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: Python3 未安装或不可用"
    exit 1
fi

# 检查必要的依赖
echo "检查依赖包..."
python3 -c "import torch, transformers, fastapi" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 某些依赖包可能未安装，尝试安装..."
    pip3 install -r requirements.txt
fi

# 检查模型文件是否存在
MODEL_PATH="$PROJECT_ROOT/my_models/optimized_model_random_20250718_032139"
if [ ! -d "$MODEL_PATH" ]; then
    echo "错误: 模型文件不存在: $MODEL_PATH"
    exit 1
fi

# 检查数据集文件是否存在
DATASET_PATH="$PROJECT_ROOT/datasets"
if [ ! -d "$DATASET_PATH" ]; then
    echo "错误: 数据集目录不存在: $DATASET_PATH"
    exit 1
fi

# 检查标签文件
LABEL_FILES=(
    "my_roberta_grammar_labels.json"
    "my_roberta_subdomain_labels.json"
    "my_roberta_entity_labels.json"
    "my_roberta_relation_labels.json"
)

for file in "${LABEL_FILES[@]}"; do
    if [ ! -f "$DATASET_PATH/$file" ]; then
        echo "错误: 标签文件不存在: $DATASET_PATH/$file"
        exit 1
    fi
done

echo "所有检查通过，正在启动API服务..."

# 设置默认端口
PORT=${1:-8000}

echo "启动参数:"
echo "  - 端口: $PORT"
echo "  - 主机: 0.0.0.0"
echo "  - 工作目录: $API_DIR"

# 启动API服务
echo "正在启动 RoBERTa API 服务..."
echo "API文档将在启动后可通过以下地址访问:"
echo "  - Swagger UI: http://localhost:$PORT/docs"
echo "  - ReDoc: http://localhost:$PORT/redoc"
echo "  - 预测端点: http://localhost:$PORT/api/v1/predict"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================="

# 使用uvicorn启动服务
python3 -m uvicorn main:app \
    --host 0.0.0.0 \
    --port $PORT \
    --reload \
    --log-level info \
    --access-log
