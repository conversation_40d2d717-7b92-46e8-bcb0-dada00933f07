import os
import sys
import torch
from transformers import AutoTokenizer
from typing import Dict, List, Any, Optional
import traceback
import time
import json

# 添加父目录到路径，以便导入现有模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 自定义异常类
class ModelError(Exception):
    """模型相关错误"""
    pass

class ValidationError(Exception):
    """数据验证错误"""
    pass

class InferenceError(Exception):
    """推理过程错误"""
    pass

class ModelService:
    """模型服务类 - 支持版本管理和异常处理"""
    
    def __init__(self):
        self.model_version = "optimized_model_random_20250718_032139"
        self.api_version = "v1"
        self.model_path = os.path.join(parent_dir, "my_models", self.model_version)
        self.dataset_path = os.path.join(parent_dir, "datasets")
        
        # 模型和tokenizer实例
        self.model = None
        self.tokenizer = None
        self.label_maps = {}
        
        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化模型，包含完整异常处理"""
        try:
            print(f"正在加载模型: {self.model_version}")
            print(f"模型路径: {self.model_path}")
            print(f"数据集路径: {self.dataset_path}")
            
            # 检查路径是否存在
            if not os.path.exists(self.model_path):
                raise ModelError(f"模型路径不存在: {self.model_path}")
            
            if not os.path.exists(self.dataset_path):
                raise ModelError(f"数据集路径不存在: {self.dataset_path}")
            
            # 导入现有模型类
            try:
                # 临时处理simple_term_menu导入问题和阻止主程序执行
                import sys
                import types

                # 创建一个虚拟的simple_term_menu模块
                mock_module = types.ModuleType('simple_term_menu')
                mock_module.TerminalMenu = lambda *args, **kwargs: None
                sys.modules['simple_term_menu'] = mock_module

                # 阻止my_roberta_conversation模块中的主程序代码执行
                # 通过设置__name__来避免模块级别的主程序代码运行
                original_name = sys.modules.get('my_roberta_conversation', {}).get('__name__', None)

                # 临时修改模块的执行环境，防止主程序代码运行
                import builtins
                original_input = builtins.input
                builtins.input = lambda *args, **kwargs: "0"  # 自动选择退出选项

                try:
                    from my_roberta_conversation import RoBERTaForConversationClassification
                    self.RoBERTaClass = RoBERTaForConversationClassification

                    # 导入模块以便设置全局变量
                    import my_roberta_conversation as roberta_module
                    self.roberta_module = roberta_module
                finally:
                    # 恢复原始的input函数
                    builtins.input = original_input

            except ImportError as e:
                raise ModelError(f"无法导入模型类: {str(e)}")
            except Exception as e:
                raise ModelError(f"模型类导入过程中发生错误: {str(e)}")
            
            # 加载标签映射
            self._load_label_maps()
            
            # 加载tokenizer
            self._load_tokenizer()
            
            # 加载模型
            self._load_model()
            
            print(f"模型加载成功: {self.model_version}")
            
        except ModelError:
            raise  # 重新抛出ModelError
        except Exception as e:
            raise ModelError(f"模型初始化失败: {str(e)}")
    
    def _load_label_maps(self):
        """加载标签映射"""
        try:
            # 定义标签文件路径
            label_files = {
                "grammar": "my_roberta_grammar_labels.json",
                "subdomain": "my_roberta_subdomain_labels.json", 
                "entity": "my_roberta_entity_labels.json",
                "relation": "my_roberta_relation_labels.json"
            }
            
            self.label_maps = {}
            
            for label_type, filename in label_files.items():
                filepath = os.path.join(self.dataset_path, filename)
                if not os.path.exists(filepath):
                    raise ModelError(f"标签文件不存在: {filepath}")
                
                # 使用现有的标签加载方法
                label_map, label_array = self.RoBERTaClass.load_labels_fromfile(filepath)
                self.label_maps[label_type] = {
                    "map": label_map, 
                    "array": label_array
                }
            
            print("标签映射加载成功")

            # 设置全局变量，供inference方法使用
            self._set_global_variables()

        except Exception as e:
            raise ModelError(f"标签映射加载失败: {str(e)}")

    def _set_global_variables(self):
        """设置my_roberta_conversation模块中需要的全局变量"""
        try:
            # 设置标签映射全局变量
            self.roberta_module.CONVERSATION_GRAMMAR_ID_MAP = self.label_maps["grammar"]["map"]
            self.roberta_module.CONVERSATION_ID_GRAMMAR_ARY = self.label_maps["grammar"]["array"]

            self.roberta_module.CONVERSATION_SUBDOMAIN_ID_MAP = self.label_maps["subdomain"]["map"]
            self.roberta_module.CONVERSATION_ID_SUBDOMAIN_ARY = self.label_maps["subdomain"]["array"]

            self.roberta_module.CONVERSATION_ENTITY_ID_MAP = self.label_maps["entity"]["map"]
            self.roberta_module.CONVERSATION_ID_ENTITY_ARY = self.label_maps["entity"]["array"]

            self.roberta_module.CONVERSATION_RELATION_ID_MAP = self.label_maps["relation"]["map"]
            self.roberta_module.CONVERSATION_ID_RELATION_ARY = self.label_maps["relation"]["array"]

            print("全局变量设置成功")

        except Exception as e:
            raise ModelError(f"全局变量设置失败: {str(e)}")
    
    def _load_tokenizer(self):
        """加载tokenizer"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            print("Tokenizer加载成功")
        except Exception as e:
            raise ModelError(f"Tokenizer加载失败: {str(e)}")
    
    def _load_model(self):
        """加载模型"""
        try:
            # 使用现有的模型加载逻辑
            self.model = self.RoBERTaClass.from_pretrained(
                self.model_path,
                grammar_label_count=len(self.label_maps["grammar"]["map"]),
                subdomain_label_count=len(self.label_maps["subdomain"]["map"])
            )
            
            # 设置为评估模式
            self.model.eval()
            
            # 移动到GPU（如果可用）
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.model.to(device)
            
            print(f"模型加载成功，设备: {device}")
            
        except Exception as e:
            raise ModelError(f"模型加载失败: {str(e)}")
    
    def predict(self, text: str) -> Dict[str, Any]:
        """执行预测，包含完整异常处理"""
        try:
            # 输入验证
            if not isinstance(text, str):
                raise ValidationError("输入必须是字符串类型")
            
            if len(text.strip()) == 0:
                raise ValidationError("输入文本不能为空")
            
            if len(text) > 512:
                raise ValidationError("输入文本长度不能超过512字符")
            
            # 执行推理
            result = self._run_inference(text)
            
            return result
            
        except ValidationError:
            raise  # 重新抛出验证错误
        except Exception as e:
            raise InferenceError(f"推理过程发生错误: {str(e)}")
    
    def _run_inference(self, text: str) -> Dict[str, Any]:
        """执行模型推理"""
        try:
            # 使用现有的inference方法
            inference_results = self.model.inference([text], self.tokenizer)
            
            if not inference_results or len(inference_results) == 0:
                raise InferenceError("模型推理返回空结果")
            
            # 格式化结果
            result = inference_results[0]
            
            formatted_result = {
                "text": result.get("sentence", text),
                "grammar": {
                    "predicted": result.get("grammar", []),
                    "confidence": result.get("grammar_confidence", 0.0)
                },
                "subdomain": {
                    "predicted": result.get("subdomain", "unknown"),
                    "confidence": result.get("subdomain_confidence", 0.0)
                },
                "entities": result.get("entities", []),
                "relations": result.get("relations", [])
            }
            
            return formatted_result
            
        except Exception as e:
            raise InferenceError(f"模型推理执行失败: {str(e)}")
    
    def get_model_version(self) -> str:
        """获取当前模型版本"""
        return self.model_version
    
    def get_api_version(self) -> str:
        """获取API版本"""
        return self.api_version
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 简单的推理测试
            test_result = self.predict("测试文本")
            return {
                "status": "healthy",
                "model_version": self.model_version,
                "api_version": self.api_version,
                "gpu_available": torch.cuda.is_available()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "model_version": self.model_version,
                "api_version": self.api_version
            }

# 为未来版本扩展预留的模型服务工厂
class ModelServiceFactory:
    """模型服务工厂 - 支持多版本模型"""
    
    @staticmethod
    def create_model_service(version: str = "v1"):
        """根据版本创建模型服务"""
        if version == "v1":
            return ModelService()
        else:
            raise ValueError(f"不支持的模型版本: {version}")
