# RoBERTa API 详细使用指南

## 📋 目录
1. [服务启动](#服务启动)
2. [接口详情](#接口详情)
3. [使用示例](#使用示例)
4. [错误处理](#错误处理)
5. [性能信息](#性能信息)

## 🚀 服务启动

### 启动命令
```bash
cd /root/TrainQuestionIntentModel/roberta_api
/opt/conda/envs/wpk_modeltrain/bin/python roberta_api_main.py
```

### 启动成功标志
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
正在初始化模型服务...
正在加载模型: optimized_model_random_20250718_032139
模型路径: /root/TrainQuestionIntentModel/my_models/optimized_model_random_20250718_032139
数据集路径: /root/TrainQuestionIntentModel/datasets
标签映射加载成功
全局变量设置成功
Tokenizer加载成功
模型加载成功，设备: cuda
模型加载成功: optimized_model_random_20250718_032139
模型服务初始化成功
INFO:     Application startup complete.
```

## 🔗 接口详情

### 1. 预测接口

**URL**: `http://127.0.0.1:8000/api/v1/predict`  
**HTTP方法**: `POST`  
**Content-Type**: `application/json`

#### 请求格式
```json
{
  "text": "输入的中文文本"
}
```
#### 预测请求示例
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的物料清单为什么没有细化完成？"}' | jq

#### 请求参数说明
| 参数 | 类型 | 必需 | 限制 | 说明 |
|------|------|------|------|------|
| text | string | 是 | 1-512字符 | 待分析的中文文本，不能为空或只包含空白字符 |

#### 响应格式
```json
{
  "success": true,
  "data": {
    "text": "项目P-94871234的项目经理是谁？",
    "grammar": {
      "predicted": ["query"],
      "confidence": 0.95
    },
    "subdomain": {
      "predicted": "baseinfo",
      "confidence": 0.88
    },
    "entities": [
      {
        "start": 3,
        "end": 8,
        "type": "Prj-Id",
        "value": "p-94871234"
      }
    ],
    "relations": [
      {
        "entity1": {
          "start": 3,
          "end": 8,
          "type": "Prj-Id",
          "value": "p-94871234"
        },
        "entity2": {
          "start": 10,
          "end": 13,
          "type": "Role-Prj-Mgr",
          "value": "项目经理"
        },
        "relation_type": "prj:has_prop"
      }
    ]
  },
  "model_version": "optimized_model_random_20250718_032139",
  "api_version": "v1",
  "processing_time_ms": 23.1
}
```

#### 响应字段说明
| 字段路径 | 类型 | 说明 |
|----------|------|------|
| success | boolean | 请求是否成功 |
| data.text | string | 处理后的输入文本 |
| data.grammar.predicted | array | 语法分类结果：query(查询)、judge(判断)、reason(原因) |
| data.grammar.confidence | float | 语法分类置信度 |
| data.subdomain.predicted | string | 子域分类：baseinfo(基本信息)、delivery(发货)、contract(合同)、unknown(未知) |
| data.subdomain.confidence | float | 子域分类置信度 |
| data.entities | array | 识别的实体列表 |
| data.entities[].start | int | 实体在文本中的起始位置 |
| data.entities[].end | int | 实体在文本中的结束位置 |
| data.entities[].type | string | 实体类型（如Prj-Id、Role-Prj-Mgr等） |
| data.entities[].value | string | 实体文本内容 |
| data.relations | array | 实体间关系列表 |
| data.relations[].entity1 | object | 关系中的第一个实体 |
| data.relations[].entity2 | object | 关系中的第二个实体 |
| data.relations[].relation_type | string | 关系类型（如prj:has_prop） |
| model_version | string | 使用的模型版本 |
| api_version | string | API版本 |
| processing_time_ms | float | 处理耗时（毫秒） |

### 2. 健康检查接口

**URL**: `http://127.0.0.1:8000/api/v1/health`  
**HTTP方法**: `GET`

#### 响应格式
```json
{
  "status": "healthy",
  "model_version": "optimized_model_random_20250718_032139",
  "api_version": "v1",
  "gpu_available": true,
  "error": null
}
```

### 3. API信息接口

**URL**: `http://127.0.0.1:8000/`  
**HTTP方法**: `GET`

#### 响应格式
```json
{
  "message": "RoBERTa Prediction API - MVP版本",
  "version": "v1",
  "docs": "/docs",
  "predict_endpoint": "/api/v1/predict",
  "health_endpoint": "/api/v1/health"
}
```

## 💻 使用示例

### curl 命令示例

#### 1. 健康检查
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/health"
```

#### 2. 基本预测
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}'
```

#### 3. 格式化输出
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}' | python3 -m json.tool
```
或者使用 jq 代替 python3 -m json.tool 输出将会是格式化好的、并且中文能够正常显示的JSON。
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}' | jq
```

```cmd
curl.exe -X POST "http://172.17.6.100:9010/api/v1/predict" -H "Content-Type: application/json" -d "{\"text\": \"项目P-94871234的项目经理是谁？\"}" | jq
```
给外部的cmd窗口去使用的示例

#### 4. 多种类型的测试文本
```bash
# 查询类问题
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-18479283的销售是谁？"}'

# 判断类问题  
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-12345678的设备是否都已经发货了？"}'

# 原因分析类问题
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-02939283为什么没有发货？"}'
```

### Python 代码示例

#### 1. 基础使用
```python
import requests
import json

# API配置
API_BASE_URL = "http://127.0.0.1:8000"
PREDICT_URL = f"{API_BASE_URL}/api/v1/predict"
HEALTH_URL = f"{API_BASE_URL}/api/v1/health"

def check_health():
    """检查API服务健康状态"""
    try:
        response = requests.get(HEALTH_URL, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务状态: {result['status']}")
            print(f"📦 模型版本: {result['model_version']}")
            print(f"🔧 API版本: {result['api_version']}")
            print(f"🚀 GPU可用: {result['gpu_available']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def predict_text(text):
    """预测文本"""
    try:
        payload = {"text": text}
        response = requests.post(
            PREDICT_URL,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 预测成功")
            print(f"📝 文本: {result['data']['text']}")
            print(f"🎯 语法: {result['data']['grammar']['predicted']}")
            print(f"🏷️  子域: {result['data']['subdomain']['predicted']}")
            print(f"🔍 实体数量: {len(result['data']['entities'])}")
            print(f"🔗 关系数量: {len(result['data']['relations'])}")
            print(f"⏱️  处理时间: {result['processing_time_ms']}ms")
            return result
        else:
            print(f"❌ 预测失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    # 检查服务状态
    if check_health():
        # 进行预测
        test_texts = [
            "项目P-94871234的项目经理是谁？",
            "项目P-18479283的销售是谁？",
            "项目P-12345678的设备是否都已经发货了？",
            "项目P-02939283为什么没有发货？"
        ]

        for text in test_texts:
            print(f"\n{'='*50}")
            print(f"测试文本: {text}")
            print('='*50)
            predict_text(text)
```

#### 2. 批量处理示例
```python
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

class RoBERTaAPIClient:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.predict_url = f"{base_url}/api/v1/predict"
        self.health_url = f"{base_url}/api/v1/health"

    def is_healthy(self):
        """检查服务是否健康"""
        try:
            response = requests.get(self.health_url, timeout=5)
            return response.status_code == 200 and response.json().get('status') == 'healthy'
        except:
            return False

    def predict(self, text, timeout=30):
        """单次预测"""
        try:
            response = requests.post(
                self.predict_url,
                json={"text": text},
                headers={"Content-Type": "application/json"},
                timeout=timeout
            )

            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}

        except Exception as e:
            return {"error": str(e)}

    def batch_predict(self, texts, max_workers=5):
        """批量预测"""
        if not self.is_healthy():
            raise Exception("API服务不可用")

        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_text = {
                executor.submit(self.predict, text): text
                for text in texts
            }

            # 收集结果
            for future in as_completed(future_to_text):
                text = future_to_text[future]
                try:
                    result = future.result()
                    results.append({
                        "text": text,
                        "result": result,
                        "success": "error" not in result
                    })
                except Exception as e:
                    results.append({
                        "text": text,
                        "result": {"error": str(e)},
                        "success": False
                    })

        return results

# 使用示例
if __name__ == "__main__":
    client = RoBERTaAPIClient()

    # 批量测试文本
    test_texts = [
        "项目P-94871234的项目经理是谁？",
        "项目P-18479283的销售是谁？",
        "技术支持和商务支持，项目P-28394857，是谁？",
        "项目P-12345678的设备是否都已经发货了？",
        "项目P-02939283为什么没有发货？"
    ]

    print("开始批量预测...")
    start_time = time.time()

    results = client.batch_predict(test_texts)

    end_time = time.time()

    # 统计结果
    success_count = sum(1 for r in results if r["success"])
    total_time = end_time - start_time

    print(f"\n📊 批量预测完成:")
    print(f"✅ 成功: {success_count}/{len(results)}")
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    print(f"🚀 平均耗时: {total_time/len(results):.2f}秒/条")

    # 显示详细结果
    for i, result in enumerate(results, 1):
        print(f"\n--- 结果 {i} ---")
        print(f"文本: {result['text']}")
        if result["success"]:
            data = result["result"]["data"]
            print(f"语法: {data['grammar']['predicted']}")
            print(f"子域: {data['subdomain']['predicted']}")
            print(f"实体: {len(data['entities'])}个")
            print(f"关系: {len(data['relations'])}个")
        else:
            print(f"错误: {result['result']['error']}")
```

## ❌ 错误处理

### HTTP状态码说明

| 状态码 | 错误类型 | 说明 | 解决方法 |
|--------|----------|------|----------|
| 200 | 成功 | 请求处理成功 | - |
| 400 | 客户端错误 | 请求参数验证失败 | 检查输入文本格式和长度 |
| 422 | 数据验证错误 | Pydantic验证失败 | 检查JSON格式和字段类型 |
| 500 | 服务器错误 | 模型推理失败 | 检查模型状态，重试请求 |
| 503 | 服务不可用 | 模型服务未初始化 | 等待服务启动完成 |

### 常见错误示例

#### 1. 空文本错误 (422)
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": ""}'

# 响应
{
  "detail": [
    {
      "type": "string_too_short",
      "loc": ["body", "text"],
      "msg": "String should have at least 1 character",
      "input": ""
    }
  ]
}
```

#### 2. 文本过长错误 (422)
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "'$(python3 -c "print('a'*600)")'"}'

# 响应
{
  "detail": [
    {
      "type": "string_too_long",
      "loc": ["body", "text"],
      "msg": "String should have at most 512 characters",
      "input": "aaa..."
    }
  ]
}
```

#### 3. 模型服务错误 (500/503)
```json
{
  "success": false,
  "error_type": "model_error",
  "error_message": "模型推理失败",
  "error_details": "具体错误信息",
  "api_version": "v1"
}
```

### Python错误处理示例
```python
import requests

def robust_predict(text, max_retries=3):
    """带重试机制的预测函数"""
    url = "http://127.0.0.1:8000/api/v1/predict"

    for attempt in range(max_retries):
        try:
            response = requests.post(
                url,
                json={"text": text},
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 400:
                print(f"❌ 输入错误: {response.json()}")
                return None  # 不重试
            elif response.status_code == 422:
                print(f"❌ 数据验证错误: {response.json()}")
                return None  # 不重试
            elif response.status_code in [500, 503]:
                print(f"⚠️  服务器错误 (尝试 {attempt+1}/{max_retries}): {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    print(f"❌ 达到最大重试次数")
                    return None
            else:
                print(f"❌ 未知错误: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            print(f"⚠️  请求超时 (尝试 {attempt+1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            else:
                print(f"❌ 请求超时，达到最大重试次数")
                return None
        except requests.exceptions.ConnectionError:
            print(f"⚠️  连接错误 (尝试 {attempt+1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            else:
                print(f"❌ 连接失败，达到最大重试次数")
                return None
        except Exception as e:
            print(f"❌ 未知异常: {e}")
            return None

    return None
```

## 📊 性能信息

### 响应时间
- **平均响应时间**: 15-30ms
- **模型加载时间**: 10-15秒（仅启动时）
- **GPU推理**: 8-15ms
- **CPU推理**: 50-100ms（如果GPU不可用）

### 处理能力
- **单次请求**: 支持1-512字符的中文文本
- **并发处理**: 建议最大并发数 ≤ 10
- **吞吐量**: 约30-50 QPS（取决于硬件配置）
- **内存使用**: 约2-4GB GPU显存

### 性能优化建议
1. **批量处理**: 使用多线程进行批量请求
2. **连接复用**: 使用session对象复用HTTP连接
3. **超时设置**: 设置合理的请求超时时间（建议30秒）
4. **错误重试**: 实现指数退避的重试机制
5. **健康检查**: 定期检查服务状态

### 性能测试示例
```python
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

def performance_test(num_requests=100, concurrency=5):
    """性能测试"""
    test_text = "项目P-94871234的项目经理是谁？"
    url = "http://127.0.0.1:8000/api/v1/predict"

    def single_request():
        start_time = time.time()
        try:
            response = requests.post(
                url,
                json={"text": test_text},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            return {
                "success": response.status_code == 200,
                "response_time": (end_time - start_time) * 1000,
                "processing_time": response.json().get("processing_time_ms", 0) if response.status_code == 200 else 0
            }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": (end_time - start_time) * 1000,
                "processing_time": 0,
                "error": str(e)
            }

    print(f"🚀 开始性能测试: {num_requests}个请求，并发数{concurrency}")
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        results = list(executor.map(lambda _: single_request(), range(num_requests)))

    end_time = time.time()

    # 统计结果
    successful_results = [r for r in results if r["success"]]
    success_rate = len(successful_results) / len(results) * 100

    if successful_results:
        response_times = [r["response_time"] for r in successful_results]
        processing_times = [r["processing_time"] for r in successful_results]

        print(f"\n📊 性能测试结果:")
        print(f"✅ 成功率: {success_rate:.1f}% ({len(successful_results)}/{len(results)})")
        print(f"⏱️  总耗时: {end_time - start_time:.2f}秒")
        print(f"🚀 QPS: {len(successful_results) / (end_time - start_time):.1f}")
        print(f"📈 响应时间统计:")
        print(f"   - 平均: {statistics.mean(response_times):.1f}ms")
        print(f"   - 中位数: {statistics.median(response_times):.1f}ms")
        print(f"   - 最小: {min(response_times):.1f}ms")
        print(f"   - 最大: {max(response_times):.1f}ms")
        print(f"🔧 模型处理时间统计:")
        print(f"   - 平均: {statistics.mean(processing_times):.1f}ms")
        print(f"   - 中位数: {statistics.median(processing_times):.1f}ms")
    else:
        print("❌ 所有请求都失败了")

# 运行性能测试
if __name__ == "__main__":
    performance_test(num_requests=50, concurrency=5)
```

## 🔍 验证API服务

### 快速验证脚本
```bash
#!/bin/bash
echo "🔍 验证RoBERTa API服务..."

# 1. 检查端口
if ! nc -z 127.0.0.1 8000; then
    echo "❌ 端口8000未开放，请检查服务是否启动"
    exit 1
fi

# 2. 健康检查
echo "📋 健康检查..."
health_response=$(curl -s http://127.0.0.1:8000/api/v1/health)
if echo "$health_response" | grep -q '"status":"healthy"'; then
    echo "✅ 服务健康"
else
    echo "❌ 服务不健康: $health_response"
    exit 1
fi

# 3. 预测测试
echo "🧪 预测功能测试..."
predict_response=$(curl -s -X POST "http://127.0.0.1:8000/api/v1/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}')

if echo "$predict_response" | grep -q '"success":true'; then
    echo "✅ 预测功能正常"
    echo "📊 响应示例:"
    echo "$predict_response" | python3 -m json.tool | head -20
else
    echo "❌ 预测功能异常: $predict_response"
    exit 1
fi

echo "🎉 API服务验证完成，一切正常！"
```

---

**📚 更多信息**:
- API文档: http://127.0.0.1:8000/docs
- ReDoc文档: http://127.0.0.1:8000/redoc
- 项目README: [README.md](README.md)
