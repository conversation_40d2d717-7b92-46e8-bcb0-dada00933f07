#!/usr/bin/env python3
"""
RoBERTa API 验证脚本
用于快速验证API服务是否正常运行
"""

import requests
import json
import time
import sys

class APIVerifier:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.predict_url = f"{base_url}/api/v1/predict"
        self.health_url = f"{base_url}/api/v1/health"
        self.root_url = f"{base_url}/"
    
    def check_connection(self):
        """检查API服务连接"""
        print("🔗 检查API服务连接...")
        try:
            response = requests.get(self.root_url, timeout=5)
            if response.status_code == 200:
                print("✅ API服务连接正常")
                return True
            else:
                print(f"❌ API服务响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到API服务，请检查服务是否启动")
            return False
        except Exception as e:
            print(f"❌ 连接检查失败: {e}")
            return False
    
    def check_health(self):
        """检查健康状态"""
        print("🏥 检查服务健康状态...")
        try:
            response = requests.get(self.health_url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                status = result.get('status')
                if status == 'healthy':
                    print("✅ 服务状态健康")
                    print(f"   📦 模型版本: {result.get('model_version')}")
                    print(f"   🔧 API版本: {result.get('api_version')}")
                    print(f"   🚀 GPU可用: {result.get('gpu_available')}")
                    return True
                else:
                    print(f"❌ 服务状态异常: {status}")
                    if result.get('error'):
                        print(f"   错误信息: {result['error']}")
                    return False
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_prediction(self):
        """测试预测功能"""
        print("🧪 测试预测功能...")
        
        test_cases = [
            {
                "text": "项目P-94871234的项目经理是谁？",
                "expected_grammar": "query",
                "expected_subdomain": "baseinfo"
            },
            {
                "text": "项目P-18479283的销售是谁？",
                "expected_grammar": "query",
                "expected_subdomain": "baseinfo"
            },
            {
                "text": "项目P-12345678的设备是否都已经发货了？",
                "expected_grammar": "judge",
                "expected_subdomain": "delivery"
            }
        ]
        
        success_count = 0
        total_time = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   测试 {i}: {test_case['text']}")
            
            try:
                start_time = time.time()
                response = requests.post(
                    self.predict_url,
                    json={"text": test_case["text"]},
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                end_time = time.time()
                request_time = (end_time - start_time) * 1000
                total_time += request_time
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        data = result['data']
                        grammar = data['grammar']['predicted']
                        subdomain = data['subdomain']['predicted']
                        entities_count = len(data['entities'])
                        relations_count = len(data['relations'])
                        processing_time = result.get('processing_time_ms', 0)
                        
                        print(f"   ✅ 预测成功 (请求耗时: {request_time:.1f}ms)")
                        print(f"      🎯 语法分类: {grammar} (期望: {test_case['expected_grammar']})")
                        print(f"      🏷️  子域分类: {subdomain} (期望: {test_case['expected_subdomain']})")
                        print(f"      🔍 实体数量: {entities_count}")
                        print(f"      🔗 关系数量: {relations_count}")
                        print(f"      ⏱️  模型处理时间: {processing_time}ms")
                        
                        # 验证预期结果
                        grammar_match = test_case['expected_grammar'] in grammar if isinstance(grammar, list) else grammar == test_case['expected_grammar']
                        subdomain_match = subdomain == test_case['expected_subdomain']
                        
                        if grammar_match and subdomain_match:
                            print(f"      ✅ 结果符合预期")
                            success_count += 1
                        else:
                            print(f"      ⚠️  结果与预期不完全匹配")
                            success_count += 0.5  # 部分成功
                    else:
                        print(f"   ❌ 预测失败: {result}")
                else:
                    print(f"   ❌ 请求失败: {response.status_code}")
                    print(f"      响应: {response.text}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        avg_time = total_time / len(test_cases) if test_cases else 0
        success_rate = (success_count / len(test_cases)) * 100 if test_cases else 0
        
        print(f"\n📊 预测测试结果:")
        print(f"   ✅ 成功率: {success_rate:.1f}% ({success_count}/{len(test_cases)})")
        print(f"   ⏱️  平均响应时间: {avg_time:.1f}ms")
        
        return success_rate >= 80  # 80%以上成功率认为通过
    
    def test_error_handling(self):
        """测试错误处理"""
        print("🚨 测试错误处理...")
        
        error_cases = [
            {"text": "", "expected_status": 422, "description": "空文本"},
            {"text": "   ", "expected_status": 422, "description": "空白文本"},
            {"text": "a" * 600, "expected_status": 422, "description": "超长文本"}
        ]
        
        success_count = 0
        
        for i, case in enumerate(error_cases, 1):
            print(f"\n   测试 {i}: {case['description']}")
            
            try:
                response = requests.post(
                    self.predict_url,
                    json={"text": case["text"]},
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if response.status_code == case["expected_status"]:
                    print(f"   ✅ 正确拒绝 {case['description']} (状态码: {response.status_code})")
                    success_count += 1
                else:
                    print(f"   ❌ 状态码不符合预期: 期望{case['expected_status']}, 实际{response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        success_rate = (success_count / len(error_cases)) * 100 if error_cases else 0
        print(f"\n📊 错误处理测试结果:")
        print(f"   ✅ 成功率: {success_rate:.1f}% ({success_count}/{len(error_cases)})")
        
        return success_rate >= 80
    
    def run_full_verification(self):
        """运行完整验证"""
        print("🚀 开始RoBERTa API完整验证")
        print("=" * 60)
        
        # 1. 连接检查
        if not self.check_connection():
            print("\n❌ 验证失败: 无法连接到API服务")
            return False
        
        print()
        
        # 2. 健康检查
        if not self.check_health():
            print("\n❌ 验证失败: 服务健康检查不通过")
            return False
        
        print()
        
        # 3. 预测功能测试
        if not self.test_prediction():
            print("\n❌ 验证失败: 预测功能测试不通过")
            return False
        
        print()
        
        # 4. 错误处理测试
        if not self.test_error_handling():
            print("\n⚠️  警告: 错误处理测试未完全通过，但不影响基本功能")
        
        print("\n" + "=" * 60)
        print("🎉 验证完成: RoBERTa API服务运行正常！")
        print("\n📚 使用指南:")
        print(f"   - API文档: {self.base_url}/docs")
        print(f"   - 健康检查: {self.health_url}")
        print(f"   - 预测接口: {self.predict_url}")
        print("   - 详细使用说明: 查看 API_USAGE_GUIDE.md")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RoBERTa API验证脚本")
    parser.add_argument("--url", default="http://127.0.0.1:8000", help="API服务地址")
    parser.add_argument("--quick", action="store_true", help="快速验证（仅健康检查）")
    
    args = parser.parse_args()
    
    verifier = APIVerifier(args.url)
    
    if args.quick:
        # 快速验证
        if verifier.check_connection() and verifier.check_health():
            print("✅ 快速验证通过")
            sys.exit(0)
        else:
            print("❌ 快速验证失败")
            sys.exit(1)
    else:
        # 完整验证
        if verifier.run_full_verification():
            sys.exit(0)
        else:
            sys.exit(1)

if __name__ == "__main__":
    main()
