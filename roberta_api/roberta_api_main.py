from fastapi import FastAPI, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any
import uvicorn
import traceback
import time
from contextlib import asynccontextmanager

# 导入模型服务
from model_loader import ModelService, ModelError, ValidationError, InferenceError

# 全局模型服务实例
model_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global model_service
    # 启动时初始化模型服务
    try:
        print("正在初始化模型服务...")
        model_service = ModelService()
        print("模型服务初始化成功")
    except Exception as e:
        print(f"模型服务初始化失败: {str(e)}")
        raise e

    yield

    # 关闭时清理资源（如果需要）
    print("正在关闭模型服务...")

# FastAPI应用配置，支持版本化
app = FastAPI(
    title="RoBERTa Prediction API",
    version="1.0.0",
    description="RoBERTa模型预测API - MVP版本，支持文本分类和实体识别",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



# 请求数据模型（包含验证）
class PredictRequest(BaseModel):
    text: str = Field(
        ...,
        min_length=1,
        max_length=512,
        description="输入文本，长度1-512字符",
        examples=["项目**********的项目经理是谁？"]
    )

    @field_validator('text')
    @classmethod
    def validate_text(cls, v: str) -> str:
        if not v.strip():
            raise ValueError('文本不能为空或只包含空白字符')
        return v.strip()

# 响应数据模型
class PredictResponse(BaseModel):
    success: bool = True
    data: Dict[str, Any]
    model_version: str
    api_version: str = "v1"
    processing_time_ms: Optional[float] = None

# 错误响应模型
class ErrorResponse(BaseModel):
    success: bool = False
    error_type: str
    error_message: str
    error_details: Optional[str] = None
    api_version: str = "v1"

# 健康检查响应模型
class HealthResponse(BaseModel):
    status: str
    model_version: str
    api_version: str
    gpu_available: bool
    error: Optional[str] = None

# 根路径 - 提供API信息
@app.get("/", response_model=Dict[str, str])
async def root():
    """API根路径，提供基本信息和文档链接"""
    return {
        "message": "RoBERTa Prediction API - MVP版本",
        "version": "v1",
        "docs": "/docs",
        "predict_endpoint": "/api/v1/predict",
        "health_endpoint": "/api/v1/health"
    }

# 健康检查端点
@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点，验证模型服务状态"""
    if model_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=ErrorResponse(
                error_type="service_unavailable",
                error_message="模型服务未初始化"
            ).model_dump()
        )
    
    try:
        health_status = model_service.health_check()
        return HealthResponse(**health_status)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse(
                error_type="health_check_error",
                error_message=str(e)
            ).model_dump()
        )

# 主预测端点
@app.post("/api/v1/predict", response_model=PredictResponse)
async def predict_v1(request: PredictRequest):
    """
    文本预测接口 v1
    
    功能：
    - 语法分类：query(查询)、judge(判断)、reason(原因分析)
    - 子域分类：baseinfo(基本信息)、delivery(发货)、contract(合同)、unknown(未知)
    - 实体识别：识别文本中的实体及其位置和类型
    - 关系抽取：识别实体间的关系
    
    使用模型：optimized_model_random_20250718_032139
    """
    if model_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=ErrorResponse(
                error_type="service_unavailable",
                error_message="模型服务未初始化"
            ).model_dump()
        )
    
    start_time = time.time()
    
    try:
        # 调用模型推理
        result = model_service.predict(request.text)
        
        processing_time = (time.time() - start_time) * 1000
        
        return PredictResponse(
            success=True,
            data=result,
            model_version=model_service.get_model_version(),
            processing_time_ms=round(processing_time, 2)
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ErrorResponse(
                error_type="validation_error",
                error_message=str(e),
                error_details="输入数据验证失败"
            ).model_dump()
        )
    except InferenceError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse(
                error_type="inference_error",
                error_message=str(e),
                error_details="模型推理过程中发生错误"
            ).model_dump()
        )
    except ModelError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=ErrorResponse(
                error_type="model_error",
                error_message=str(e),
                error_details="模型服务不可用"
            ).model_dump()
        )
    except Exception as e:
        # 记录详细错误信息用于调试
        error_details = traceback.format_exc()
        print(f"系统错误: {error_details}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse(
                error_type="system_error",
                error_message="系统内部错误",
                error_details=str(e)  # 在MVP阶段显示详细错误信息
            ).model_dump()
        )

# 自定义异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    error_details = traceback.format_exc()
    print(f"未捕获的异常: {error_details}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error_type="unexpected_error",
            error_message="发生未预期的错误",
            error_details=str(exc)
        ).model_dump()
    )

if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run(
        "roberta_api_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式，代码变更时自动重载
        log_level="info"
    )
