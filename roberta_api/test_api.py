#!/usr/bin/env python3
"""
RoBERTa API 测试脚本
用于验证API功能正确性和性能
"""

import requests
import json
import time
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class APITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.predict_url = f"{base_url}/api/v1/predict"
        self.health_url = f"{base_url}/api/v1/health"
        
    def test_health_check(self):
        """测试健康检查端点"""
        print("🔍 测试健康检查端点...")
        try:
            response = requests.get(self.health_url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 健康检查通过")
                print(f"   状态: {result.get('status')}")
                print(f"   模型版本: {result.get('model_version')}")
                print(f"   API版本: {result.get('api_version')}")
                print(f"   GPU可用: {result.get('gpu_available')}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False
    
    def test_predict_single(self, text, expected_grammar=None):
        """测试单个预测请求"""
        print(f"🔍 测试预测: {text}")
        try:
            payload = {"text": text}
            start_time = time.time()
            
            response = requests.post(
                self.predict_url, 
                json=payload, 
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            request_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 预测成功 (请求耗时: {request_time:.2f}ms)")
                
                # 显示结果
                data = result.get('data', {})
                print(f"   文本: {data.get('text')}")
                print(f"   语法分类: {data.get('grammar', {}).get('predicted')} (置信度: {data.get('grammar', {}).get('confidence', 0):.3f})")
                print(f"   子域分类: {data.get('subdomain', {}).get('predicted')} (置信度: {data.get('subdomain', {}).get('confidence', 0):.3f})")
                print(f"   实体数量: {len(data.get('entities', []))}")
                print(f"   关系数量: {len(data.get('relations', []))}")
                print(f"   模型版本: {result.get('model_version')}")
                print(f"   处理时间: {result.get('processing_time_ms')}ms")
                
                # 显示实体详情
                entities = data.get('entities', [])
                if entities:
                    print("   实体详情:")
                    for i, entity in enumerate(entities[:5]):  # 只显示前5个
                        print(f"     {i+1}. [{entity.get('start')}:{entity.get('end')}] {entity.get('type')}: {entity.get('value')}")
                    if len(entities) > 5:
                        print(f"     ... 还有 {len(entities) - 5} 个实体")
                
                return True
            else:
                print(f"❌ 预测失败: HTTP {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误类型: {error_detail.get('error_type')}")
                    print(f"   错误信息: {error_detail.get('error_message')}")
                except:
                    print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 预测异常: {str(e)}")
            return False
    
    def test_validation_errors(self):
        """测试输入验证错误"""
        print("🔍 测试输入验证...")
        
        test_cases = [
            ("", "空文本"),
            ("   ", "空白文本"),
            ("a" * 600, "超长文本"),
        ]
        
        for text, description in test_cases:
            print(f"   测试 {description}...")
            try:
                payload = {"text": text}
                response = requests.post(
                    self.predict_url, 
                    json=payload, 
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if response.status_code == 400:
                    print(f"   ✅ 正确拒绝 {description}")
                else:
                    print(f"   ❌ 应该拒绝 {description}，但返回: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 测试 {description} 异常: {str(e)}")
    
    def load_test_data(self):
        """加载现有的测试数据"""
        test_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "datasets", "my_roberta_v2_testdata.txt"
        )
        
        if os.path.exists(test_file):
            with open(test_file, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        else:
            print(f"⚠️  测试数据文件不存在: {test_file}")
            return []
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始 RoBERTa API 综合测试")
        print("=" * 50)
        
        # 1. 健康检查
        if not self.test_health_check():
            print("❌ 健康检查失败，停止测试")
            return False
        
        print()
        
        # 2. 基础预测测试
        print("📝 基础预测测试")
        basic_tests = [
            "项目P-94871234的项目经理是谁？",
            "项目P-18479283的销售是谁？",
            "项目P-02939283为什么没有发货？",
            "项目P-19839283的服务器和保护为什么没有发货？",
            "项目P-12345678的设备是否都已经发货了？"
        ]
        
        success_count = 0
        for text in basic_tests:
            if self.test_predict_single(text):
                success_count += 1
            print()
        
        print(f"基础测试结果: {success_count}/{len(basic_tests)} 成功")
        print()
        
        # 3. 输入验证测试
        self.test_validation_errors()
        print()
        
        # 4. 使用现有测试数据
        test_data = self.load_test_data()
        if test_data:
            print(f"📊 使用现有测试数据 ({len(test_data)} 条)")
            sample_size = min(5, len(test_data))
            sample_data = test_data[:sample_size]
            
            sample_success = 0
            for text in sample_data:
                if self.test_predict_single(text):
                    sample_success += 1
                print()
            
            print(f"样本测试结果: {sample_success}/{sample_size} 成功")
        
        print("=" * 50)
        print("🎉 测试完成")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RoBERTa API 测试脚本")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务地址")
    parser.add_argument("--text", help="测试单个文本")
    parser.add_argument("--health", action="store_true", help="只进行健康检查")
    
    args = parser.parse_args()
    
    tester = APITester(args.url)
    
    if args.health:
        # 只进行健康检查
        tester.test_health_check()
    elif args.text:
        # 测试单个文本
        tester.test_predict_single(args.text)
    else:
        # 运行综合测试
        tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
