from transformers import <PERSON>Tokenizer, TrainingArguments, Trainer
from transformers import AutoModel, AutoModelForCausalLM, AutoModelForSeq2SeqLM, AutoModelForTokenClassification, BertForTokenClassification, BertTokenizer, BertModel
import torch

from torch.utils.data import IterableDataset
from colorama import Fore, Style
import numpy
from fuzzywuzzy import fuzz as fuzzy

import os

from my_roberta_conversation import RoBERTaForConversationClassification as rbfcc

DEFAULT_MODEL_NAMES = [
    "Qwen/Qwen2.5-0.5B-Instruct",
    "Qwen/Qwen2.5-1.5B-Instruct",
    "hfl/chinese-roberta-wwm-ext",
    "google-t5/t5-large",
    "my/multitask-roberta"
]

label_id_map = {
    "O": 0,
    "B-PROJ": 1,
    "I-PROJ": 2,
    "B-PROJID": 3,
    "I-PROJID": 4,
    "B-PROJMGR": 5,
    "I-PROJMGR": 6,
}
id_label_ary = list(label_id_map.keys())

intent_id_map = {
    "Unknown": 0,
    "Query-Entity-Property": 1
}
id_intent_ary = list(intent_id_map.keys())


def input_model_path(tip_info):

    print(tip_info)
    print("0. Return.")
    for i in range(len(DEFAULT_MODEL_NAMES)):
        print(f"{i+1}. {DEFAULT_MODEL_NAMES[i]}")
    print("Choice:(option or modelname)", end=" ")

    model_path = ""
    option = input()

    if option.isdigit():
        option = int(option) 
        if (option == 0):
            return model_path
        elif (option > 0) and (option <= len(DEFAULT_MODEL_NAMES)):
            model_path = DEFAULT_MODEL_NAMES[option-1]
        else:
            print("Invalid option. Exiting...")
    else:
        model_path = option

    return model_path

def download_model_from_huggingface(model_name, save_path="./dl_models"):

    print(f"sta --- downloaded {model_name} to {save_path}")

    # 离线模式，从huggingface下载
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModel.from_pretrained(model_name, device_map="auto")
    tokenizer.save_pretrained(save_path+"/"+model_name)
    model.save_pretrained(save_path+"/"+model_name)

    print("end --- downloaded model to localpath")

def load_model_from_local_path(model_name, model_path="./dl_models"):

    print(f"sta --- load {model_name} model from {model_path}")

    if model_name == DEFAULT_MODEL_NAMES[0]:
        tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+model_name)
        model = AutoModelForCausalLM.from_pretrained(model_path+"/"+model_name, device_map="auto")
    elif model_name == DEFAULT_MODEL_NAMES[1]:
        tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+model_name)
        model = AutoModelForCausalLM.from_pretrained(model_path+"/"+model_name, device_map="auto")
    elif model_name == DEFAULT_MODEL_NAMES[2]:

        tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+model_name)
    
        num_labels = len(label_id_map)
        label_ids = list(label_id_map.values())
        label_strs = list(label_id_map.keys())
        
        # importantly, the label id map must contain all the labels in the dataset
        print(Fore.GREEN + f"laod bert model with num label {num_labels}" + Style.RESET_ALL)
        print(Fore.GREEN + f"laod bert model with label id map {label_id_map}" + Style.RESET_ALL)

        model = BertModel.from_pretrained(
            model_path+"/"+model_name, 
            num_labels=num_labels,
            id2label=label_ids,
            label2id=label_strs,)
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        
    elif model_name == DEFAULT_MODEL_NAMES[3]:
        tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+model_name)
        model = AutoModelForSeq2SeqLM.from_pretrained(model_path+"/"+model_name, device_map="auto", from_pt=True)
    # if is customieze multitask model
    elif model_name == DEFAULT_MODEL_NAMES[4]:
        # if the default file is empty then load from original path
        if (os.path.exists(model_path+"/"+model_name) == False):
            tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+DEFAULT_MODEL_NAMES[2])
            model = MultiTaskRoBERTa.from_pretrained(
                model_path+"/"+DEFAULT_MODEL_NAMES[2],
                num_labels=num_labels,
                id2label=label_ids,
                label2id=label_strs,)
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model.to(device)
        # else load from my model path
        else:
            tokenizer = AutoTokenizer.from_pretrained(model_path+"/"+model_name)
            model = MultiTaskRoBERTa.from_pretrained(
                model_path+"/"+model_name,
                num_labels=num_labels,
                label2id=label_strs,)
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model.to(device)

    else:
        print("Invalid model name. Exiting...")

    print("end --- load model from local path")
    print(f'Model is on device: {model.device}')

    # enable gradient checkpointing to save memory usage
    model.gradient_checkpointing_enable()

    # print model parameters
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Total Number of Parameters: {total_params / 1e6:.2f} M")

    # print memory usage
    device = torch.device("cuda:0")  # Change to your desired GPU index
    print(f"Allocated memory: {torch.cuda.memory_allocated(device) / 1024**3:.2f} GB")
    print(f"Reserved memory: {torch.cuda.memory_reserved(device) / 1024**3:.2f} GB")

    return model, tokenizer

def preprocess_stream_conversion_dataset(dataset, tokenizer, max_length=1024, padding="max_length", truncation=True):

    messages = dataset.get("conversation", [])

    if messages == [] or len(messages) < 2 or messages[-1]["role"] != "assistant":
        return {}
    
    prompt_text = messages[:-1]
    response_text = messages[-1]["content"]

    try:
        prompt = tokenizer.apply_chat_template(prompt_text, tokenize=False, add_generation_prompt=True)
        model_inputs = tokenizer(prompt, padding=padding, truncation=truncation, max_length=max_length)
        labels = tokenizer(response_text, padding=padding, truncation=truncation, max_length=max_length)
        model_inputs["labels"] = labels["input_ids"]
        return model_inputs
    except Exception:
        return {}

def preprecess_stream_classification_dataset(dataset, tokenizer, max_length=256, padding="max_length", truncation=True):

    messages = dataset.get("sentence", [])
    if messages == []:
        return {}
    
    model_inputs = tokenizer(messages.get("content"), padding=padding, truncation=truncation, max_length=max_length)


    # Align labels with tokenized input
    # -100 is the value used for padding tokens in PyTorch
    # because the dataset was mannually aligned the token and label
    # so here i just mannually replace the word_id content with label id
    word_ids = model_inputs.word_ids(0)
    labels = messages.get("labels")
    # set item is none to -100
    # hard code set item from 1 to lable id, CLS,lebel,SEP
    # here can not use word_ids.copy, because word_ids is a list, copy is a shadow copy
    aligned_label_ids = []
    for idx, word_id in enumerate(word_ids):
        if word_id is None:
            aligned_label_ids.append(-100)
        elif 1 <= idx <= len(labels):
            aligned_label_ids.append(label_id_map.get(labels[idx - 1], -100))
        else:
            aligned_label_ids.append(-100)

    model_inputs["labels"] = aligned_label_ids

    #print(f"sentence: {messages.get('content')}")
    #print(f"tokens: {messages.get('tokens')}")
    #print(f"labels: {labels}")
    #print(f"model_inputs[{len(model_inputs['input_ids'])}]: {model_inputs['input_ids']}")
    #print(f"model_masks[{len(model_inputs['attention_mask'])}]: {model_inputs['attention_mask']}")
    #print(f"model_labels[{len(model_inputs['labels'])}]: {model_inputs['labels']}")
    #print(f"word_ids: {word_ids}")

    # tokenize here, so i did not need to mannual use with_format("torch") in train
    return {
        "input_ids": torch.tensor(model_inputs["input_ids"]),
        "attention_mask": torch.tensor(model_inputs["attention_mask"]),
        "labels": torch.tensor(model_inputs["labels"]),
    }


def extract_entities(labels):
    """
    提取实体边界,根据BIO标签来识别实体。
    输入: BIO标签序列
    输出: 实体列表（每个实体作为一个标签，如'PER','ORG'等）
    """
    entities = []
    current_entity = None

    for label in labels:
        if label.startswith("B-"):  # Entity Start
            # 如果已经有一个实体在进行中，先保存它
            if current_entity is not None:
                entities.append(current_entity)
            # 开始一个新的实体
            current_entity = label[2:]  # 取掉"B-"，只保留实体类型
        elif label.startswith("I-"):  # Inside an Entity
            if current_entity is not None:
                current_entity += label[2:]  # 继续构建当前实体
        else:  # O or other non-entity labels
            if current_entity is not None:
                entities.append(current_entity)
            current_entity = None  # 结束当前实体

    # 如果循环结束时有实体尚未保存，保存它
    if current_entity is not None:
        entities.append(current_entity)

    return entities

def extract_entities(labels, tokens, sentence):
    """
    提取实体边界,根据BIO标签来识别实体。
    输入: BIO标签序列
    输出: 实体列表（每个实体作为一个标签，如'PER','ORG'等）
    """
    entities = []
    current_entity = None
    current_entity_type = None

    for (label, token) in zip(labels, tokens):
        if label.startswith("B-"):  # Entity Start
            # 如果已经有一个实体在进行中，先保存它
            if current_entity is not None:
                entities.append({"entity": current_entity_type, "content": current_entity})
            # 开始一个新的实体
            current_entity = token # 连接实体
            current_entity_type = label[2:] # 取掉"B-"，只保留实体类型
        elif label.startswith("I-"):  # Inside an Entity
            # with subword it start with ##
            if token.startswith("##"):
                current_entity += token[2:]  # 继续构建当前实体
            else:
                current_entity += token
        else:  # O or other non-entity labels
            if current_entity is not None:
                entities.append({"entity": current_entity_type, "content": current_entity})
            current_entity = None  # 结束当前实体

    # 如果循环结束时有实体尚未保存，保存它
    if current_entity is not None:
        entities.append({"entity": current_entity_type, "content": current_entity})

    return entities

def compare_fuzzy_entities(results):

    error_sentences = 0
    total_sentences = len(results)

    # loop every sentence in results
    for result in results:

        # get pred and true entities list
        pred_entities = result["pred"]
        true_entities = result["true"]

        # if lenth of pred_entities and true_entities is not equal, then it is a error sentence
        if (len(pred_entities) != len(true_entities)):
            error_sentences += 1
            continue

        # compare entity if identical and content if similar
        for idx, (pred_entity, true_entity) in enumerate(zip(pred_entities, true_entities)):
            if pred_entity["entity"] == true_entity["entity"] and fuzzy.ratio(pred_entity["content"],true_entity["content"]) > 80:
                continue
            else:
                error_sentences += 1
                break

    # 计算准确度
    precision = (total_sentences - error_sentences) / total_sentences if total_sentences > 0 else 0

    return precision

def decode_predictions_results(pred_labels, true_labels, eval_dataset):

    results = [] 
    for rowidx, (eval, pred_label, true_label) in enumerate(zip(eval_dataset, pred_labels, true_labels)):

        #g get the sentence content, tokens and labels
        sencence = eval["sentence"]["content"]
        tokens = eval["sentence"]["tokens"]

        mask = true_label != -100 # use the mask in true label to filter the pred_label
        pred_label = pred_label[mask]  # 仅保留非填充的预测标签
        true_label = true_label[mask]  # 仅保留非填充的真实标签

        pred_entities = extract_entities([id_label_ary[id] for id in pred_label], tokens, sencence) # 提取预测的类型/实体列表
        true_entities = extract_entities([id_label_ary[id] for id in true_label], tokens, sencence) # 提取真实的类型/实体列表

        results.append({
            "sentence": sencence,
            "pred": pred_entities,
            "true": true_entities
        })

    return results

def compute_metrics_classification(eval_pred, eval_dataset):

    predictions, labels = eval_pred

    correct_predictions = 0
    total_predictions = 0
    total_labels = 0

    # 获取预测的标签
    pred_labels = numpy.argmax(predictions, axis=-1)  

    # pred_labels, labels, eval_dataset, all have the likely shape, len(pred_labels) == len(labels) == len(eval_dataset)
    # loop eval_dataeset's sentence, and get the token list
    results = decode_predictions_results(pred_labels, labels, eval_dataset)
 
    # calculate the metrics
    precision = compare_fuzzy_entities(results)

    return {
        "precision": precision
    }

def train_with_specific_dataset(model, tokenizer, train_dataset, eval_dataset, max_row_count=1000, custom_epoch=3):

    # different model have different dataset processing routine
    # if model type is bert for calssification, use token classification dataset
    if isinstance(model, BertForTokenClassification) :
        tokenized_train_dataset = train_dataset.map(lambda x: preprecess_stream_classification_dataset(x, tokenizer), batched=False, remove_columns=["sentence"])
        tokenized_eval_dataset = eval_dataset.map(lambda x: preprecess_stream_classification_dataset(x, tokenizer), batched=False, remove_columns=["sentence"])

    training_args = TrainingArguments(
        max_steps=max_row_count*custom_epoch,
        output_dir="./results",             # 保存模型的目录
        overwrite_output_dir=True,          # 是否覆盖输出目录
        save_strategy="no",              # 保存策略
        save_steps=100,                    # 每1000步保存一次
        save_total_limit=1,                 # 限制保存的模型数量
        eval_strategy="steps",              # 每个epoch后进行评估
        eval_steps=max_row_count // 2,
        per_device_train_batch_size=1,      # 每个设备的批量大小
        #per_device_eval_batch_size=evalbatch,       # 评估时的批量大小
        gradient_accumulation_steps=1,      # Accumulate gradients over 4 steps
        num_train_epochs=1,                 # 训练的轮数
        logging_dir='./logs',               # 日志目录
        logging_steps=100,                  # 每100步输出日志
        #fp16=True,
        warmup_ratio=0.01,                   # 学习率线性增加的步数
        lr_scheduler_type="cosine",         # 学习率调度器类型
        #weight_decay=0.05,                  # 权重衰减
        report_to="tensorboard"             # 输出到tensorboard
        #learning_rate=2e-5,                 # 学习率
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_train_dataset,
        eval_dataset=tokenized_eval_dataset,
        processing_class=tokenizer,
        compute_metrics=lambda eval_pred: compute_metrics_classification(eval_pred, eval_dataset)
    )

    trainer.train()

def eval_with_specific_dataset(model, tokenizer, eval_dataset):

    if isinstance(model, BertForTokenClassification) :
        tokenized_eval_dataset = eval_dataset.map(lambda x: preprecess_stream_classification_dataset(x, tokenizer), batched=False, remove_columns=["sentence"])

    training_args = TrainingArguments(
        output_dir="./results",             # 保存模型的目录
        overwrite_output_dir=True,          # 是否覆盖输出目录
        eval_strategy="no",              # 每个epoch后进行评估
        do_train=False,
        do_eval=False,
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        processing_class=tokenizer
    )

    predictions, labels, metrics = trainer.predict(tokenized_eval_dataset)

    # 获取预测的标签
    pred_labels = numpy.argmax(predictions, axis=-1)  

    # pred_labels, labels, eval_dataset, all have the likely shape, len(pred_labels) == len(labels) == len(eval_dataset)
    # loop eval_dataeset's sentence, and get the token list
    results = decode_predictions_results(pred_labels, labels, eval_dataset)

    # print all results
    for result in results:

        error_sentence = False
        # get pred and true entities list
        pred_entities = result["pred"]
        true_entities = result["true"]

        # if lenth of pred_entities and true_entities is not equal, then it is a error sentence
        if (len(pred_entities) != len(true_entities)):
            error_sentence = True
        else:
            # compare entity if identical and content if similar
            for idx, (pred_entity, true_entity) in enumerate(zip(pred_entities, true_entities)):
                if pred_entity["entity"] == true_entity["entity"] and fuzzy.ratio(pred_entity["content"],true_entity["content"]) > 80:
                    continue
                else:
                    error_sentences = True
                    break

        color = Fore.YELLOW if error_sentence else ""
        reset = Fore.RESET if error_sentence else ""
        
        print(" ")
        print(f"{color}sentence: {result['sentence']}{reset}")
        print(f"{color}pred: {result['pred']}{reset}")
        print(f"{color}true: {result['true']}{reset}")
        print(" ")
        
