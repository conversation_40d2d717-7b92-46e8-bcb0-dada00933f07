#!/usr/bin/env python3
"""
测试compute_metrics修复是否正确
"""

import torch
import numpy as np
from my_roberta_entity_relation import compute_metrics

def test_compute_metrics_function():
    """
    测试compute_metrics函数是否能正常工作
    """
    print("🧪 测试compute_metrics函数修复")
    print("=" * 50)
    
    try:
        # 创建模拟的评估数据
        batch_size = 2
        seq_length = 128
        num_entities = 78
        num_relations = 17
        
        # 模拟预测结果 (preds)
        start_logits = torch.randn(batch_size, seq_length)  # 实体开始位置logits
        end_logits = torch.randn(batch_size, seq_length)    # 实体结束位置logits
        span_logits = torch.randn(batch_size, 10, num_entities)  # 实体类型logits
        relation_logits = torch.randn(batch_size, 5, num_relations)  # 关系类型logits
        
        preds = [start_logits, end_logits, span_logits, relation_logits]
        
        # 模拟真实标签 (labels)
        entity_start_labels = torch.randint(0, 2, (batch_size, seq_length))
        entity_end_labels = torch.randint(0, 2, (batch_size, seq_length))
        
        # 模拟span标签 (start, end, label)
        span_entity_labels = [
            [(10, 15, 5), (20, 25, 8), (-1, -1, -1)],  # 第一个样本
            [(5, 10, 3), (-1, -1, -1), (-1, -1, -1)]   # 第二个样本
        ]
        
        # 模拟关系标签 (start_i, end_i, start_j, end_j, type_id)
        entity_relation_labels = [
            [(10, 15, 20, 25, 2), (-1, -1, -1, -1, -1)],  # 第一个样本
            [(-1, -1, -1, -1, -1), (-1, -1, -1, -1, -1)]  # 第二个样本
        ]
        
        labels = [entity_start_labels, entity_end_labels, span_entity_labels, entity_relation_labels]
        
        # 创建eval_pred元组
        eval_pred = (preds, labels)
        
        print("✅ 模拟数据创建成功")
        print(f"   • 批次大小: {batch_size}")
        print(f"   • 序列长度: {seq_length}")
        print(f"   • 实体类型数: {num_entities}")
        print(f"   • 关系类型数: {num_relations}")
        
        # 测试compute_metrics函数
        print("\n🔍 测试compute_metrics函数...")
        
        metrics = compute_metrics(eval_pred)
        
        print("✅ compute_metrics函数调用成功!")
        print("\n📊 返回的评估指标:")
        for metric_name, metric_value in metrics.items():
            print(f"   • {metric_name}: {metric_value:.4f}")
        
        # 验证返回的指标
        expected_metrics = ['start_end_f1', 'span_f1', 'relation_f1']
        missing_metrics = [m for m in expected_metrics if m not in metrics]
        
        if missing_metrics:
            print(f"❌ 缺少指标: {missing_metrics}")
            return False
        
        # 验证指标值的合理性
        for metric_name, metric_value in metrics.items():
            if not (0 <= metric_value <= 1):
                print(f"❌ 指标值超出范围 [0,1]: {metric_name} = {metric_value}")
                return False
        
        print("✅ 所有指标都在合理范围内")
        
        return True
        
    except Exception as e:
        print(f"❌ compute_metrics函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compute_metrics_with_logging():
    """
    测试带日志的compute_metrics函数
    """
    print(f"\n🧪 测试compute_metrics_with_logging函数")
    print("=" * 50)
    
    try:
        # 导入训练函数中的compute_metrics_with_logging
        # 由于它是在函数内部定义的，我们需要模拟它的行为
        
        # 创建简单的测试数据
        batch_size = 1
        seq_length = 64
        
        start_logits = torch.randn(batch_size, seq_length)
        end_logits = torch.randn(batch_size, seq_length)
        span_logits = torch.randn(batch_size, 5, 78)
        relation_logits = torch.randn(batch_size, 3, 17)
        
        preds = [start_logits, end_logits, span_logits, relation_logits]
        
        entity_start_labels = torch.randint(0, 2, (batch_size, seq_length))
        entity_end_labels = torch.randint(0, 2, (batch_size, seq_length))
        span_entity_labels = [[(5, 10, 3), (-1, -1, -1)]]
        entity_relation_labels = [[(-1, -1, -1, -1, -1)]]
        
        labels = [entity_start_labels, entity_end_labels, span_entity_labels, entity_relation_labels]
        eval_pred = (preds, labels)
        
        # 模拟compute_metrics_with_logging的行为
        def mock_compute_metrics_with_logging(eval_pred):
            metrics = compute_metrics(eval_pred)
            
            print(f"\n📊 评估结果:")
            for metric_name, metric_value in metrics.items():
                print(f"  • {metric_name}: {metric_value:.4f}")
            
            return metrics
        
        print("🔍 测试带日志的评估函数...")
        metrics = mock_compute_metrics_with_logging(eval_pred)
        
        print("✅ 带日志的评估函数工作正常!")
        
        return True
        
    except Exception as e:
        print(f"❌ 带日志的评估函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 compute_metrics修复验证工具")
    print("=" * 60)
    
    # 测试基础compute_metrics函数
    test1_passed = test_compute_metrics_function()
    
    # 测试带日志的compute_metrics函数
    test2_passed = test_compute_metrics_with_logging()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   {'✅' if test1_passed else '❌'} compute_metrics函数")
    print(f"   {'✅' if test2_passed else '❌'} compute_metrics_with_logging函数")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 所有测试通过！compute_metrics修复成功！")
        print(f"\n💡 现在可以安全地运行训练:")
        print(f"   python my_roberta_entity_relation.py")
        print(f"\n📊 训练过程中将正确显示评估指标:")
        print(f"   • start_end_f1: 实体边界检测F1分数")
        print(f"   • span_f1: 实体类型分类F1分数") 
        print(f"   • relation_f1: 关系分类F1分数")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查修复")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    main()
