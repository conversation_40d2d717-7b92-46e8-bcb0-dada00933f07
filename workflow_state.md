# 工作流状态与规则 (STM + Rules + Log)

*此文件包含当前会话的动态状态、嵌入式规则、活动计划和日志。*
*在操作循环期间，AI会频繁读取和更新它。*

---

## State

*保存工作流的当前状态。*

```yaml
Phase: ANALYZE # 当前工作流阶段 (ANALYZE, BLUEPRINT, CONSTRUCT, VALIDATE, BLUEPRINT_REVISE)
Status: COMPLETED # 当前状态 (READY, IN_PROGRESS, BLOCKED_*, NEEDS_*, COMPLETED)
CurrentTaskID: fa0e2a13-4275-4b72-a161-ca4b341c3023 # 正在处理的主要任务标识符
CurrentStep: 更新项目配置 # 正在执行的计划中特定步骤的标识符
```

---

## Plan

*包含在BLUEPRINT阶段生成的逐步实施计划。*
*(此部分将由AI在BLUEPRINT阶段填充)*

*示例:*
*   `[ ] 步骤1: 创建文件 src/utils/helper.ts`
*   `[ ] 步骤2: 在helper.ts中实现'calculateSum'函数`
*   `[ ] 步骤3: 为'calculateSum'添加单元测试`

---

## Rules

*管理AI自主操作的嵌入式规则。*

**# --- 核心工作流规则 ---**

RULE_WF_PHASE_ANALYZE:
  **约束:** 目标是理解请求/上下文。不进行解决方案设计或实施计划。

RULE_WF_PHASE_BLUEPRINT:
  **约束:** 目标是创建详细、明确的逐步计划。不进行代码实现。

RULE_WF_PHASE_CONSTRUCT:
  **约束:** 目标是精确执行`## Plan`。不允许偏离。如果出现问题，触发错误处理或回退阶段。

RULE_WF_PHASE_VALIDATE:
  **约束:** 目标是使用工具验证实现是否符合`## Plan`和需求。不进行新的实现。

RULE_WF_TRANSITION_01:
  **触发条件:** 明确的用户命令(`@analyze`, `@blueprint`, `@construct`, `@validate`)。
  **操作:** 相应更新`State.Phase`。记录阶段变更。

RULE_WF_TRANSITION_02:
  **触发条件:** AI确定当前阶段约束阻止履行用户请求，或错误处理指示阶段变更(例如，RULE_ERR_HANDLE_TEST_01)。
  **操作:** 记录原因。更新`State.Phase`(例如，改为`BLUEPRINT_REVISE`)。适当设置`State.Status`(例如，`NEEDS_PLAN_APPROVAL`)。向用户报告。

**# --- 初始化与恢复规则 ---**

RULE_INIT_01:
  **触发条件:** AI会话/任务启动且`workflow_state.md`缺失或为空。
  **操作:**
    1. 使用默认结构创建`workflow_state.md`。
    2. 读取`project_config.md`(如果缺失则提示用户)。
    3. 设置`State.Phase = ANALYZE`, `State.Status = READY`。
    4. 记录"初始化新会话。"
    5. 提示用户输入第一个任务。

RULE_INIT_02:
  **触发条件:** AI会话/任务启动且`workflow_state.md`存在。
  **操作:**
    1. 读取`project_config.md`。
    2. 读取现有的`workflow_state.md`。
    3. 记录"恢复会话。"
    4. 检查`State.Status`: 适当处理READY, COMPLETED, BLOCKED_*, NEEDS_*, IN_PROGRESS状态(提示用户或报告状态)。

RULE_INIT_03:
  **触发条件:** 用户通过RULE_INIT_02确认继续(针对IN_PROGRESS状态)。
  **操作:** 基于加载的状态和规则继续执行下一个操作。

**# --- 内存管理规则 ---**

RULE_MEM_READ_LTM_01:
  **触发条件:** 新的主要任务或阶段开始。
  **操作:** 读取`project_config.md`。记录操作。

RULE_MEM_READ_STM_01:
  **触发条件:** 每个决策/行动周期之前。
  **操作:** 读取`workflow_state.md`。

RULE_MEM_UPDATE_STM_01:
  **触发条件:** 每次重要操作或信息接收之后。
  **操作:** 立即更新`workflow_state.md`中的相关部分(`## State`, `## Plan`, `## Log`)并保存。

RULE_MEM_UPDATE_LTM_01:
  **触发条件:** 用户命令(`@config/update`)或重大更改的VALIDATE阶段成功结束。
  **操作:** 基于`## Log`/差异，提出对`project_config.md`的简洁更新。设置`State.Status = NEEDS_LTM_APPROVAL`。等待用户确认。

RULE_MEM_VALIDATE_01:
  **触发条件:** 更新`workflow_state.md`或`project_config.md`后。
  **操作:** 执行内部一致性检查。如发现问题，记录并设置`State.Status = NEEDS_CLARIFICATION`。

**# --- 工具集成规则 (Cursor环境) ---**

RULE_TOOL_LINT_01:
  **触发条件:** CONSTRUCT阶段保存相关源文件。
  **操作:** 指示Cursor终端运行lint命令。记录尝试。完成后，解析输出，记录结果，如有错误则设置`State.Status = BLOCKED_LINT`。

RULE_TOOL_FORMAT_01:
  **触发条件:** CONSTRUCT阶段保存相关源文件。
  **操作:** 指示Cursor应用格式化工具或通过终端运行格式化命令。记录尝试。

RULE_TOOL_TEST_RUN_01:
  **触发条件:** 命令`@validate`或进入VALIDATE阶段。
  **操作:** 指示Cursor终端运行测试套件。记录尝试。完成后，解析输出，记录结果，如失败则设置`State.Status = BLOCKED_TEST`，如成功则设置为`TESTS_PASSED`。

RULE_TOOL_APPLY_CODE_01:
  **触发条件:** AI根据CONSTRUCT阶段的`## Plan`确定需要代码更改。
  **操作:** 生成修改。指示Cursor应用它。记录操作。

**# --- 错误处理与恢复规则 ---**

RULE_ERR_HANDLE_LINT_01:
  **触发条件:** `State.Status`为`BLOCKED_LINT`。
  **操作:** 分析`## Log`中的错误。如简单/有信心，尝试自动修复。通过RULE_TOOL_APPLY_CODE_01应用修复。通过RULE_TOOL_LINT_01重新运行lint。如成功，重置`State.Status`。如失败/复杂，设置`State.Status = BLOCKED_LINT_UNRESOLVED`，向用户报告。

RULE_ERR_HANDLE_TEST_01:
  **触发条件:** `State.Status`为`BLOCKED_TEST`。
  **操作:** 分析`## Log`中的失败。如简单/局部/有信心，尝试自动修复。通过RULE_TOOL_APPLY_CODE_01应用修复。通过RULE_TOOL_TEST_RUN_01重新运行失败的测试或套件。如成功，重置`State.Status`。如失败/复杂，设置`State.Phase = BLUEPRINT_REVISE`，`State.Status = NEEDS_PLAN_APPROVAL`，基于失败分析提出修订的`## Plan`，向用户报告。

RULE_ERR_HANDLE_GENERAL_01:
  **触发条件:** 意外错误或歧义。
  **操作:** 将错误/情况记录到`## Log`。设置`State.Status = BLOCKED_UNKNOWN`。向用户报告，请求指示。

---

## Log

*重要操作、事件、工具输出和决策的时间顺序日志。*
*(此部分将由AI在操作期间填充)*

*示例:*
*   `[2025-03-26 17:55:00] 初始化新会话。`
*   `[2025-03-26 17:55:15] 用户任务: 实现登录功能。`
*   `[2025-03-26 17:55:20] State.Phase更改为ANALYZE。`
*   `[2025-03-26 17:56:00] 读取project_config.md。`
*   ...

*实际日志:*
*   `[2025-06-24 17:53:47] 初始化新会话。状态设置为ANALYZE/READY。`
*   `[2025-06-24 18:00:00] 开始代码库全面分析任务。`
*   `[2025-06-24 18:05:00] 完成项目概述分析 - 识别为基于RoBERTa的中文对话理解系统。`
*   `[2025-06-24 18:10:00] 完成软件架构分析 - 多任务学习架构，包含意图分类、实体识别、关系抽取。`
*   `[2025-06-24 18:15:00] 完成核心模块分析 - RoBERTaForConversationClassification类详细分析。`
*   `[2025-06-24 18:20:00] 完成数据架构分析 - 标签体系、数据格式、处理工作流。`
*   `[2025-06-24 18:25:00] 完成技术栈分析 - PyTorch + HuggingFace生态系统。`
*   `[2025-06-24 18:30:00] 生成完整技术文档 - 包含架构图和详细说明。`
*   `[2025-06-24 18:35:00] 更新项目配置文件 - project_config.md和workflow_state.md。`