# my_roberta - 中文对话理解系统

基于RoBERTa的多任务中文对话理解系统，专门用于项目管理领域的智能问答和信息抽取。

## 🎯 项目特点

- **多任务学习**: 同时处理意图分类、实体识别和关系抽取
- **中文优化**: 基于chinese-roberta-wwm-ext预训练模型
- **项目管理专用**: 针对项目信息查询和发货管理场景
- **易于扩展**: 支持新领域和新任务的快速适配
- **完整工具链**: 从数据标注到模型训练的端到端解决方案

## 🚀 快速开始

### 1. 环境检查
```bash
# 运行快速启动脚本
python 快速启动脚本.py
```

### 2. 基本使用
```bash
# 启动主程序
python my_roberta_conversation.py

# 按照菜单提示操作:
# 3 -> 加载模型
# 5 -> 运行推理测试
```

### 3. 测试示例
```python
# 输入测试句子
"项目P-94871234的项目经理是谁？"

# 预期输出
Grammar: ['query']
Subdomain: baseinfo
Entities:
  - [02:13] (B-PROJID) P-94871234
  - [14:17] (B-ROLE-MGR) 项目经理
Relations:
  - [02:13] (B-PROJID) (P-94871234) =prj:has_prop= [14:17] (B-ROLE-MGR) (项目经理)
```

## 📚 文档结构

### 核心文档
- **[技术文档.md](技术文档.md)** - 完整的技术架构和实现细节
- **[用户使用说明.md](用户使用说明.md)** - 详细的使用指南和训练教程
- **[project_config.md](project_config.md)** - 项目配置和技术栈信息
- **[workflow_state.md](workflow_state.md)** - 工作流状态和开发日志

### 示例和工具
- **[发货领域训练数据示例.json](发货领域训练数据示例.json)** - 发货领域的标注数据示例
- **[快速启动脚本.py](快速启动脚本.py)** - 环境检查和快速测试工具

## 🏗️ 系统架构

```
输入文本 → 分词器 → RoBERTa编码器 → 多任务分类器 → 结构化输出
                                    ├── 意图分类
                                    ├── 子域分类  
                                    ├── 实体识别
                                    └── 关系抽取
```

## 📊 支持的任务类型

### 意图分类
- **Query**: 查询类问题（"谁是项目经理？"）
- **Judge**: 判断类问题（"设备发货了吗？"）
- **Reason**: 推理类问题（"为什么没有发货？"）

### 子域分类
- **BaseInfo**: 基础信息查询（人员、角色等）
- **Delivery**: 发货信息查询（时间、地点、状态等）
- **Contract**: 合同相关信息

### 实体识别
- **项目相关**: PROJID（项目编号）、PROJ（项目名称）
- **角色相关**: ROLE-MGR（项目经理）、ROLE-DES（设计）、ROLE-TST（测试）
- **发货相关**: DELIVERY-ITEM（发货物品）、DELIVERY-DATE（发货日期）、DELIVERY-STATUS（发货状态）

## 🔧 技术栈

- **深度学习**: PyTorch + HuggingFace Transformers
- **预训练模型**: chinese-roberta-wwm-ext
- **数据处理**: HuggingFace Datasets + scikit-learn
- **标注工具**: Label Studio
- **监控工具**: TensorBoard

## 📈 性能指标

基于测试数据集的评估结果：
- **意图分类 F1**: 0.91
- **子域分类准确率**: 0.95
- **实体识别 F1**: 0.86
- **关系抽取 F1**: 0.82

## 🎓 使用场景

### 1. 项目信息查询
```
用户: "项目P-001的技术支持是谁？"
系统: 识别项目编号P-001和角色类型技术支持，查询对应人员信息
```

### 2. 发货状态查询
```
用户: "P-002的服务器什么时候发货？"
系统: 识别项目编号P-002和设备类型服务器，查询发货时间
```

### 3. 多实体关系查询
```
用户: "项目P-003的服务器和保护都在1#箱子里吗？"
系统: 识别项目、设备类型和位置信息，分析实体间关系
```

## 🔄 二次开发指南

### 新领域适配
1. **准备标注数据**: 使用Label Studio标注领域特定数据
2. **更新标签体系**: 修改对应的JSON配置文件
3. **增量训练**: 基于现有模型进行领域适配训练
4. **评估优化**: 使用验证集评估并调优模型参数

### 新任务添加
1. **扩展模型架构**: 在RoBERTaForConversationClassification中添加新的分类器
2. **更新损失函数**: 在forward方法中添加新任务的损失计算
3. **扩展评估指标**: 在compute_metrics中添加新任务的评估方法
4. **数据格式适配**: 更新preprocess_dataset方法支持新任务数据

## 🐛 常见问题

### 环境问题
- **CUDA内存不足**: 减小批大小或使用梯度累积
- **依赖版本冲突**: 使用虚拟环境隔离依赖
- **中文编码问题**: 确保文件编码为UTF-8

### 模型问题
- **推理结果为空**: 调整分类阈值或检查输入格式
- **训练不收敛**: 调整学习率和训练参数
- **过拟合**: 增加正则化或减少训练轮数

### 数据问题
- **标注不一致**: 建立标注规范和质量检查流程
- **数据不平衡**: 使用数据增强或调整损失权重
- **领域适配差**: 增加目标领域的标注数据

## 🤝 贡献指南

1. **Fork** 项目到你的GitHub账户
2. **创建** 特性分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 你的修改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

- **文档**: 查看[用户使用说明.md](用户使用说明.md)获取详细帮助
- **问题反馈**: 在GitHub Issues中提交问题
- **技术讨论**: 参与GitHub Discussions

## 🙏 致谢

- HuggingFace团队提供的Transformers库和预训练模型
- 哈工大讯飞联合实验室的chinese-roberta-wwm-ext模型
- Label Studio团队提供的优秀标注工具

---

**开始你的智能对话理解之旅！** 🚀
