# 项目配置

## 项目概述
- **项目名称**: my_roberta - 基于RoBERTa的中文对话理解系统
- **项目目标**: 构建一个能够理解中文对话的多任务自然语言处理模型，专门用于项目管理领域的对话理解和信息抽取
- **项目背景**: 针对项目管理场景中的信息查询需求，开发智能对话理解系统，支持项目信息查询、交付信息查询等业务场景
- **项目需求**:
  - 多任务对话理解（意图分类、实体识别、关系抽取）
  - 中文项目管理领域适配
  - 对话上下文管理
  - 缺失信息智能提示

## 技术栈
- **编程语言**: Python 3.x
- **深度学习框架**: PyTorch
- **预训练模型**: HuggingFace Transformers (chinese-roberta-wwm-ext)
- **数据处理**: HuggingFace Datasets, NumPy, scikit-learn
- **开发工具**: Label Studio (数据标注), TensorBoard (模型监控), simple-term-menu (交互界面)
- **其他工具**: Git版本控制, JSON数据格式

## 关键模式与约定
- **命名规范**:
  - 类名使用PascalCase (如: RoBERTaForConversationClassification)
  - 方法名使用snake_case (如: preprocess_dataset)
  - 常量使用UPPER_CASE (如: MAX_ENTITY_SENTENCE)
- **代码组织**:
  - 单文件架构，所有核心功能集中在my_roberta_conversation.py
  - 配置文件分离，标签定义存储在datasets/目录下的JSON文件中
  - 模型文件按HuggingFace标准格式组织
- **错误处理**:
  - 使用try-catch处理文件IO和模型加载异常
  - 数据预处理中包含详细的警告信息
  - 支持优雅的程序退出和终端恢复
- **测试策略**:
  - 基于HuggingFace Trainer的内置评估
  - 多指标评估（F1分数、准确率等）
  - 推理测试使用独立的测试数据文件

## 项目限制
- **性能要求**:
  - 支持GPU加速训练和推理
  - 最大序列长度限制为256 tokens
  - 批处理大小根据显存动态调整
- **兼容性要求**:
  - 兼容HuggingFace Transformers 4.49.0+
  - 支持CUDA和CPU运行环境
  - 中文文本处理和字符编码支持
- **安全性要求**:
  - 模型文件安全存储
  - 训练数据隐私保护
  - 推理结果不包含敏感信息
- **其他限制**:
  - 专门针对项目管理领域，泛化能力有限
  - 依赖预训练模型，需要足够的计算资源
  - 标注数据质量直接影响模型性能


