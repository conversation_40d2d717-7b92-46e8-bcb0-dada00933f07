#!/usr/bin/env python3
"""
Comprehensive Loss Visualization Module for RoBERTa Training
"""

import os
import glob
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

try:
    from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available. Install with: pip install tensorboard")
    TENSORBOARD_AVAILABLE = False

try:
    import seaborn as sns
    sns.set_style("whitegrid")
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

class LossVisualizer:
    """Comprehensive loss visualization for RoBERTa training"""
    
    def __init__(self, logs_dir="./logs", output_dir="./visualizations"):
        self.logs_dir = logs_dir
        self.output_dir = output_dir
        self.loss_components = [
            'train/loss', 'eval/loss', 'train/grammar_loss', 'train/subdomain_loss',
            'train/position_loss', 'train/entity_type_loss', 'train/relation_loss'
        ]
        self.metrics_components = [
            'eval/grammar_f1', 'eval/subdomain_acc', 'eval/start_end_f1', 
            'eval/span_f1', 'eval/relation_f1'
        ]
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
    def parse_tensorboard_logs(self, log_path):
        """Parse TensorBoard logs and extract loss/metrics data"""
        if not TENSORBOARD_AVAILABLE:
            print("TensorBoard not available for log parsing")
            return {}
            
        try:
            ea = EventAccumulator(log_path)
            ea.Reload()
            
            data = {}
            
            # Extract scalar data
            for tag in ea.Tags()['scalars']:
                scalar_events = ea.Scalars(tag)
                steps = [event.step for event in scalar_events]
                values = [event.value for event in scalar_events]
                wall_times = [event.wall_time for event in scalar_events]
                
                data[tag] = {
                    'steps': steps,
                    'values': values,
                    'wall_times': wall_times
                }
                
            return data
            
        except Exception as e:
            print(f"Error parsing {log_path}: {e}")
            return {}
    
    def get_latest_training_session_logs(self):
        """Get logs from the most recent training session only"""
        all_log_files = []

        # Collect all log files with their modification times
        # Main training logs
        main_logs = glob.glob(os.path.join(self.logs_dir, "events.out.tfevents.*"))
        for log_file in main_logs:
            try:
                mod_time = os.path.getmtime(log_file)
                all_log_files.append((mod_time, log_file, 'main'))
            except OSError:
                continue

        # Staged training logs
        stage_dirs = ['stage_1_foundation', 'stage_2_entity_types', 'stage_3_relations']
        for stage in stage_dirs:
            stage_path = os.path.join(self.logs_dir, stage)
            if os.path.exists(stage_path):
                stage_logs = glob.glob(os.path.join(stage_path, "events.out.tfevents.*"))
                for log_file in stage_logs:
                    try:
                        mod_time = os.path.getmtime(log_file)
                        all_log_files.append((mod_time, log_file, stage))
                    except OSError:
                        continue

        if not all_log_files:
            return {}

        # Sort by modification time (newest first)
        all_log_files.sort(key=lambda x: x[0], reverse=True)

        # Get the most recent log file
        latest_time, latest_file, latest_type = all_log_files[0]
        latest_time_str = datetime.fromtimestamp(latest_time).strftime('%Y-%m-%d %H:%M:%S')

        print(f"🔍 Most recent training session detected:")
        print(f"   Type: {latest_type}")
        print(f"   File: {os.path.basename(latest_file)}")
        print(f"   Time: {latest_time_str}")

        # Determine training session threshold (files within 1 hour of the latest)
        session_threshold = latest_time - 3600  # 1 hour

        # Group logs by training type and filter by session threshold
        log_dirs = {}
        session_files = [f for f in all_log_files if f[0] >= session_threshold]

        # If the latest file is from main training, only include main logs from this session
        if latest_type == 'main':
            main_session_files = [f[1] for f in session_files if f[2] == 'main']
            if main_session_files:
                log_dirs['main'] = main_session_files
                print(f"   Using {len(main_session_files)} main training log files from latest session")
        else:
            # If the latest file is from staged training, include all staged logs from this session
            for stage in stage_dirs:
                stage_session_files = [f[1] for f in session_files if f[2] == stage]
                if stage_session_files:
                    log_dirs[stage] = stage_session_files
                    print(f"   Using {len(stage_session_files)} {stage} log files from latest session")

        return log_dirs

    def find_log_directories(self):
        """Find all log directories and categorize them - now uses latest session detection"""
        # First try to get logs from the latest training session only
        latest_session_logs = self.get_latest_training_session_logs()
        if latest_session_logs:
            return latest_session_logs

        # Fallback to original logic if no recent logs found
        print("⚠️ No recent training session detected, using all available logs")
        log_dirs = {}

        # Find main training logs
        main_logs = glob.glob(os.path.join(self.logs_dir, "events.out.tfevents.*"))
        if main_logs:
            log_dirs['main'] = main_logs

        # Find staged training logs
        stage_dirs = ['stage_1_foundation', 'stage_2_entity_types', 'stage_3_relations']
        for stage in stage_dirs:
            stage_path = os.path.join(self.logs_dir, stage)
            if os.path.exists(stage_path):
                stage_logs = glob.glob(os.path.join(stage_path, "events.out.tfevents.*"))
                if stage_logs:
                    log_dirs[stage] = stage_logs

        return log_dirs
    
    def detect_training_issues(self, train_loss, val_loss, steps):
        """Detect overfitting, underfitting, and other training issues"""
        issues = []
        recommendations = []
        
        if len(train_loss) < 5 or len(val_loss) < 5:
            return issues, recommendations
            
        # Calculate moving averages for trend analysis
        window = min(5, len(train_loss) // 3)
        train_ma = np.convolve(train_loss, np.ones(window)/window, mode='valid')
        val_ma = np.convolve(val_loss, np.ones(window)/window, mode='valid')
        
        # Detect overfitting: validation loss increasing while training loss decreasing
        if len(train_ma) > 3 and len(val_ma) > 3:
            train_trend = np.polyfit(range(len(train_ma)), train_ma, 1)[0]
            val_trend = np.polyfit(range(len(val_ma)), val_ma, 1)[0]
            
            if train_trend < -0.01 and val_trend > 0.01:
                issues.append("Overfitting detected")
                recommendations.append("Consider early stopping or regularization")
                
            # Detect underfitting: both losses plateauing at high values
            recent_train = train_ma[-3:]
            recent_val = val_ma[-3:]
            
            if (np.std(recent_train) < 0.01 and np.std(recent_val) < 0.01 and 
                np.mean(recent_train) > 1.0):
                issues.append("Underfitting detected")
                recommendations.append("Consider increasing model capacity or learning rate")
        
        # Calculate train-validation gap
        if len(train_loss) > 0 and len(val_loss) > 0:
            final_gap = abs(val_loss[-1] - train_loss[-1])
            if final_gap > 0.5:
                issues.append(f"Large train-val gap: {final_gap:.3f}")
                recommendations.append("Consider more regularization or data augmentation")
                
        return issues, recommendations
    
    def smooth_curve(self, values, alpha=0.6):
        """Apply exponential smoothing to curves"""
        if len(values) == 0:
            return values

        smoothed = [values[0]]
        for i in range(1, len(values)):
            smoothed.append(alpha * values[i] + (1 - alpha) * smoothed[-1])
        return smoothed

    def plot_train_val_comparison(self, data_dict, title_prefix="Training", save_path=None):
        """Plot training vs validation loss comparison on the same axes"""

        # Check if both training and validation loss are available
        if 'train/loss' not in data_dict or 'eval/loss' not in data_dict:
            print("Both training and validation loss required for comparison plot")
            return None

        train_data = data_dict['train/loss']
        val_data = data_dict['eval/loss']

        if not train_data['values'] or not val_data['values']:
            print("No loss data available for comparison")
            return None

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # Plot training loss (blue solid line)
        train_steps = train_data['steps']
        train_values = train_data['values']
        ax.plot(train_steps, train_values, color='#1f77b4', linewidth=2,
                label='Training Loss', linestyle='-', marker='o', markersize=3, alpha=0.8)

        # Plot validation loss (red dashed line)
        val_steps = val_data['steps']
        val_values = val_data['values']
        ax.plot(val_steps, val_values, color='#d62728', linewidth=2,
                label='Validation Loss', linestyle='--', marker='s', markersize=3, alpha=0.8)

        # Set labels and title
        ax.set_xlabel('Training Steps', fontsize=12)
        ax.set_ylabel('Loss Value', fontsize=12)
        ax.set_title(f'{title_prefix} - Training vs Validation Loss Comparison', fontsize=14, fontweight='bold')

        # Add grid for easier reading
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.grid(True, alpha=0.1, linestyle='-', linewidth=0.5, which='minor')

        # Add legend
        ax.legend(fontsize=11, loc='upper right')

        # Add final value annotations
        if train_values and val_values:
            final_train = train_values[-1]
            final_val = val_values[-1]
            final_train_step = train_steps[-1] if train_steps else 0
            final_val_step = val_steps[-1] if val_steps else 0

            # Annotate final training loss
            ax.annotate(f'Final Train: {final_train:.4f}',
                       xy=(final_train_step, final_train),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'),
                       fontsize=10)

            # Annotate final validation loss
            ax.annotate(f'Final Val: {final_val:.4f}',
                       xy=(final_val_step, final_val),
                       xytext=(10, -20), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'),
                       fontsize=10)

            # Highlight divergence if overfitting detected
            if len(train_values) >= 5 and len(val_values) >= 5:
                # Check for divergence in the last portion of training
                recent_train = train_values[-5:]
                recent_val = val_values[-5:]
                recent_train_steps = train_steps[-5:] if len(train_steps) >= 5 else train_steps
                recent_val_steps = val_steps[-5:] if len(val_steps) >= 5 else val_steps

                train_trend = np.polyfit(range(len(recent_train)), recent_train, 1)[0]
                val_trend = np.polyfit(range(len(recent_val)), recent_val, 1)[0]

                # If training loss decreasing but validation increasing (overfitting)
                if train_trend < -0.01 and val_trend > 0.01:
                    # Find approximate divergence point
                    divergence_step = max(recent_train_steps[0], recent_val_steps[0])
                    ax.axvline(x=divergence_step, color='orange', linestyle=':', linewidth=2, alpha=0.7)
                    ax.text(divergence_step, ax.get_ylim()[1] * 0.9,
                           'Potential Overfitting\nDetected',
                           rotation=90, verticalalignment='top', horizontalalignment='right',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                           fontsize=9)

        # Ensure both curves use the same scale
        all_values = train_values + val_values
        if all_values:
            y_min = min(all_values) * 0.95
            y_max = max(all_values) * 1.05
            ax.set_ylim(y_min, y_max)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Train/Val comparison plot saved to {save_path}")

        return fig

    def plot_loss_components(self, data_dict, title_prefix="Training", save_path=None):
        """Plot individual loss components in subplots"""

        # Define loss component mapping (excluding total losses as they're handled separately)
        loss_mapping = {
            'train/grammar_loss': 'Grammar Loss',
            'train/subdomain_loss': 'Subdomain Loss',
            'train/position_loss': 'Position Loss',
            'train/entity_type_loss': 'Entity Type Loss',
            'train/relation_loss': 'Relation Loss'
        }

        # Filter available loss components
        available_losses = {k: v for k, v in loss_mapping.items() if k in data_dict}

        if not available_losses:
            print("No individual loss components found to plot")
            return None

        # Create subplots
        n_plots = len(available_losses)
        cols = 3
        rows = (n_plots + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(15, 4*rows))
        if rows == 1:
            axes = [axes] if n_plots == 1 else axes
        else:
            axes = axes.flatten()

        # Plot each loss component
        for idx, (loss_key, loss_name) in enumerate(available_losses.items()):
            ax = axes[idx] if n_plots > 1 else axes

            loss_data = data_dict[loss_key]
            steps = loss_data['steps']
            values = loss_data['values']

            # Plot raw data without smoothing (as requested)
            ax.plot(steps, values, color='blue', linewidth=2, label=loss_name,
                   marker='o', markersize=2, alpha=0.8)

            ax.set_xlabel('Steps')
            ax.set_ylabel('Loss')
            ax.set_title(f'{title_prefix} - {loss_name}')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Add final value annotation
            if values:
                final_value = values[-1]
                final_step = steps[-1] if steps else 0
                ax.annotate(f'Final: {final_value:.4f}',
                           xy=(final_step, final_value),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7),
                           fontsize=9)

        # Hide unused subplots
        for idx in range(n_plots, len(axes)):
            axes[idx].set_visible(False)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Loss components plot saved to {save_path}")

        return fig

    def plot_staged_training(self, staged_data, save_path=None):
        """Plot staged training with clear demarcation between stages"""

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()

        # Define colors for each stage
        stage_colors = {
            'stage_1_foundation': '#FF6B6B',
            'stage_2_entity_types': '#4ECDC4',
            'stage_3_relations': '#45B7D1'
        }

        stage_names = {
            'stage_1_foundation': 'Stage 1: Foundation',
            'stage_2_entity_types': 'Stage 2: Entity Types',
            'stage_3_relations': 'Stage 3: Relations'
        }

        # Plot 1: Total Loss across stages
        ax1 = axes[0]
        step_offset = 0

        for stage, data in staged_data.items():
            if 'train/loss' in data:
                steps = np.array(data['train/loss']['steps']) + step_offset
                values = data['train/loss']['values']
                smoothed = self.smooth_curve(values)

                color = stage_colors.get(stage, '#333333')
                ax1.plot(steps, values, alpha=0.3, color=color, linewidth=1)
                ax1.plot(steps, smoothed, color=color, linewidth=2,
                        label=stage_names.get(stage, stage))

                step_offset = steps[-1] if len(steps) > 0 else step_offset

        ax1.set_xlabel('Steps')
        ax1.set_ylabel('Loss')
        ax1.set_title('Total Loss - Staged Training')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Individual loss components
        ax2 = axes[1]
        loss_types = ['train/grammar_loss', 'train/entity_type_loss', 'train/relation_loss']

        for loss_type in loss_types:
            step_offset = 0
            all_steps = []
            all_values = []

            for stage, data in staged_data.items():
                if loss_type in data:
                    steps = np.array(data[loss_type]['steps']) + step_offset
                    values = data[loss_type]['values']
                    all_steps.extend(steps)
                    all_values.extend(values)
                    step_offset = steps[-1] if len(steps) > 0 else step_offset

            if all_steps:
                smoothed = self.smooth_curve(all_values)
                loss_name = loss_type.split('/')[-1].replace('_', ' ').title()
                ax2.plot(all_steps, smoothed, linewidth=2, label=loss_name)

        ax2.set_xlabel('Steps')
        ax2.set_ylabel('Loss')
        ax2.set_title('Loss Components - Staged Training')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Validation metrics (if available)
        ax3 = axes[2]
        metrics = ['eval/grammar_f1', 'eval/span_f1', 'eval/relation_f1']

        for metric in metrics:
            step_offset = 0
            all_steps = []
            all_values = []

            for stage, data in staged_data.items():
                if metric in data:
                    steps = np.array(data[metric]['steps']) + step_offset
                    values = data[metric]['values']
                    all_steps.extend(steps)
                    all_values.extend(values)
                    step_offset = steps[-1] if len(steps) > 0 else step_offset

            if all_steps:
                smoothed = self.smooth_curve(all_values)
                metric_name = metric.split('/')[-1].replace('_', ' ').title()
                ax3.plot(all_steps, smoothed, linewidth=2, label=metric_name)

        ax3.set_xlabel('Steps')
        ax3.set_ylabel('Score')
        ax3.set_title('Validation Metrics - Staged Training')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Plot 4: Training summary with stage boundaries
        ax4 = axes[3]
        step_offset = 0
        stage_boundaries = []

        for stage, data in staged_data.items():
            if 'train/loss' in data:
                steps = np.array(data['train/loss']['steps']) + step_offset
                if len(steps) > 0:
                    stage_boundaries.append((step_offset, steps[-1], stage_names.get(stage, stage)))
                    step_offset = steps[-1]

        # Plot stage boundaries
        for start, end, name in stage_boundaries:
            ax4.axvspan(start, end, alpha=0.2, label=name)
            ax4.text((start + end) / 2, 0.5, name, rotation=90,
                    verticalalignment='center', horizontalalignment='center')

        ax4.set_xlabel('Steps')
        ax4.set_title('Training Stages Overview')
        ax4.set_ylim(0, 1)
        ax4.legend()

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Staged training plot saved to {save_path}")

        return fig

    def plot_staged_train_val_comparison(self, staged_data, save_path=None):
        """Plot staged training vs validation loss comparison across all stages"""

        # Define colors for each stage
        stage_colors = {
            'stage_1_foundation': '#FF6B6B',
            'stage_2_entity_types': '#4ECDC4',
            'stage_3_relations': '#45B7D1'
        }

        stage_names = {
            'stage_1_foundation': 'Stage 1: Foundation',
            'stage_2_entity_types': 'Stage 2: Entity Types',
            'stage_3_relations': 'Stage 3: Relations'
        }

        # Create figure
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))

        # Collect all training and validation data across stages
        step_offset = 0
        stage_boundaries = []

        for stage, data in staged_data.items():
            if 'train/loss' in data and 'eval/loss' in data:
                train_data = data['train/loss']
                val_data = data['eval/loss']

                if train_data['values'] and val_data['values']:
                    color = stage_colors.get(stage, '#333333')
                    stage_name = stage_names.get(stage, stage)

                    # Adjust steps with offset
                    train_steps = [s + step_offset for s in train_data['steps']]
                    val_steps = [s + step_offset for s in val_data['steps']]

                    # Plot training loss for this stage
                    ax.plot(train_steps, train_data['values'],
                           color=color, linewidth=2, linestyle='-',
                           label=f'{stage_name} - Train', marker='o', markersize=2, alpha=0.8)

                    # Plot validation loss for this stage
                    ax.plot(val_steps, val_data['values'],
                           color=color, linewidth=2, linestyle='--',
                           label=f'{stage_name} - Val', marker='s', markersize=2, alpha=0.8)

                    # Record stage boundaries
                    stage_start = step_offset
                    stage_end = max(train_steps[-1] if train_steps else step_offset,
                                  val_steps[-1] if val_steps else step_offset)
                    stage_boundaries.append((stage_start, stage_end, stage_name, color))

                    # Update offset for next stage
                    step_offset = stage_end

        # Add stage boundary indicators
        for start, end, name, color in stage_boundaries:
            # Add vertical lines at stage boundaries
            if start > 0:  # Don't add line at the very beginning
                ax.axvline(x=start, color=color, linestyle=':', alpha=0.5, linewidth=1)

        # Set labels and title
        ax.set_xlabel('Training Steps', fontsize=12)
        ax.set_ylabel('Loss Value', fontsize=12)
        ax.set_title('Staged Training - Train vs Validation Loss Progression', fontsize=14, fontweight='bold')

        # Add grid
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # Add legend
        ax.legend(fontsize=10, loc='upper right', ncol=2)

        # Add final annotations for each stage
        y_pos = 0.95
        for stage, data in staged_data.items():
            if 'train/loss' in data and 'eval/loss' in data:
                train_values = data['train/loss']['values']
                val_values = data['eval/loss']['values']

                if train_values and val_values:
                    stage_name = stage_names.get(stage, stage)
                    final_train = train_values[-1]
                    final_val = val_values[-1]
                    gap = abs(final_val - final_train)

                    # Add text box with stage summary
                    summary_text = f"{stage_name}:\nTrain: {final_train:.4f}\nVal: {final_val:.4f}\nGap: {gap:.4f}"
                    ax.text(0.02, y_pos, summary_text, transform=ax.transAxes,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor=stage_colors.get(stage, '#333333'), alpha=0.2),
                           fontsize=9, verticalalignment='top')
                    y_pos -= 0.25

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Staged train/val comparison plot saved to {save_path}")

        return fig

    def generate_training_report(self, data_dict, issues, recommendations, save_path=None):
        """Generate a comprehensive training report"""

        report = []
        report.append("# Training Analysis Report")
        report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Training summary
        report.append("## Training Summary")
        total_steps = 0
        for key, data in data_dict.items():
            if 'train/loss' in data:
                steps = data['train/loss']['steps']
                if steps:
                    total_steps = max(total_steps, max(steps))

        report.append(f"- Total training steps: {total_steps}")
        report.append(f"- Log components found: {len(data_dict)}")
        report.append("")

        # Issues and recommendations
        if issues:
            report.append("## ⚠️ Training Issues Detected")
            for issue in issues:
                report.append(f"- {issue}")
            report.append("")

        if recommendations:
            report.append("## 💡 Recommendations")
            for rec in recommendations:
                report.append(f"- {rec}")
            report.append("")

        # Final metrics
        report.append("## Final Metrics")
        for key, data in data_dict.items():
            for metric in self.metrics_components:
                if metric in data and data[metric]['values']:
                    final_value = data[metric]['values'][-1]
                    metric_name = metric.split('/')[-1].replace('_', ' ').title()
                    report.append(f"- {metric_name}: {final_value:.4f}")

        report_text = "\n".join(report)

        if save_path:
            with open(save_path, 'w') as f:
                f.write(report_text)
            print(f"Training report saved to {save_path}")

        return report_text

    def visualize_training_loss(self, run_name=None, show_plots=True, save_plots=True):
        """Main function to visualize training loss and generate analysis"""

        print("🔍 Analyzing training logs...")

        # Find log directories
        log_dirs = self.find_log_directories()

        if not log_dirs:
            print("❌ No training logs found in", self.logs_dir)
            return None

        print(f"📊 Found {len(log_dirs)} log directories")

        # Parse all log data
        all_data = {}
        staged_data = {}

        for log_type, log_files in log_dirs.items():
            print(f"   Processing {log_type}...")

            combined_data = defaultdict(lambda: {'steps': [], 'values': [], 'wall_times': []})

            for log_file in log_files:
                data = self.parse_tensorboard_logs(log_file)

                # Combine data from multiple files
                for tag, tag_data in data.items():
                    combined_data[tag]['steps'].extend(tag_data['steps'])
                    combined_data[tag]['values'].extend(tag_data['values'])
                    combined_data[tag]['wall_times'].extend(tag_data['wall_times'])

            # Sort by steps
            for tag in combined_data:
                sorted_indices = np.argsort(combined_data[tag]['steps'])
                combined_data[tag]['steps'] = [combined_data[tag]['steps'][i] for i in sorted_indices]
                combined_data[tag]['values'] = [combined_data[tag]['values'][i] for i in sorted_indices]
                combined_data[tag]['wall_times'] = [combined_data[tag]['wall_times'][i] for i in sorted_indices]

            all_data[log_type] = dict(combined_data)

            if log_type.startswith('stage_'):
                staged_data[log_type] = dict(combined_data)

        # Generate timestamp for file names
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_suffix = f"_{run_name}" if run_name else ""

        # Detect training issues
        issues = []
        recommendations = []

        for log_type, data in all_data.items():
            if 'train/loss' in data and 'eval/loss' in data:
                train_loss = data['train/loss']['values']
                val_loss = data['eval/loss']['values']
                steps = data['train/loss']['steps']

                type_issues, type_recs = self.detect_training_issues(train_loss, val_loss, steps)
                issues.extend([f"[{log_type}] {issue}" for issue in type_issues])
                recommendations.extend([f"[{log_type}] {rec}" for rec in type_recs])

        # Generate visualizations
        figures = []

        # 1. Train/Val comparison plots (priority - first to generate)
        for log_type, data in all_data.items():
            if data and 'train/loss' in data and 'eval/loss' in data:
                title = f"Training Analysis - {log_type.replace('_', ' ').title()}"
                save_path = os.path.join(self.output_dir, f"train_val_comparison_{log_type}_{timestamp}{run_suffix}.png") if save_plots else None

                fig = self.plot_train_val_comparison(data, title, save_path)
                if fig:
                    figures.append((f"{log_type}_train_val_comparison", fig))

        # 2. Individual loss component plots
        for log_type, data in all_data.items():
            if data:
                title = f"Training Analysis - {log_type.replace('_', ' ').title()}"
                save_path = os.path.join(self.output_dir, f"loss_components_{log_type}_{timestamp}{run_suffix}.png") if save_plots else None

                fig = self.plot_loss_components(data, title, save_path)
                if fig:
                    figures.append((f"{log_type}_components", fig))

        # 3. Staged training overview
        if staged_data:
            save_path = os.path.join(self.output_dir, f"staged_training_{timestamp}{run_suffix}.png") if save_plots else None
            fig = self.plot_staged_training(staged_data, save_path)
            if fig:
                figures.append(("staged_training", fig))

            # Also create a combined staged train/val comparison
            save_path_combined = os.path.join(self.output_dir, f"staged_train_val_comparison_{timestamp}{run_suffix}.png") if save_plots else None
            fig_combined = self.plot_staged_train_val_comparison(staged_data, save_path_combined)
            if fig_combined:
                figures.append(("staged_train_val_comparison", fig_combined))

        # 3. Generate report
        report_path = os.path.join(self.output_dir, f"training_report_{timestamp}{run_suffix}.md") if save_plots else None
        report = self.generate_training_report(all_data, issues, recommendations, report_path)

        # Display results
        print("\n📈 Visualization Results:")
        print(f"   Generated {len(figures)} plots")
        print(f"   Detected {len(issues)} issues")
        print(f"   Generated {len(recommendations)} recommendations")

        if issues:
            print("\n⚠️ Training Issues:")
            for issue in issues:
                print(f"   - {issue}")

        if recommendations:
            print("\n💡 Recommendations:")
            for rec in recommendations:
                print(f"   - {rec}")

        if show_plots:
            plt.show()

        return {
            'figures': figures,
            'issues': issues,
            'recommendations': recommendations,
            'report': report,
            'data': all_data
        }


def visualize_training_loss(logs_dir="./logs", output_dir="./visualizations",
                          run_name=None, show_plots=True, save_plots=True):
    """
    Standalone function to visualize training loss

    Args:
        logs_dir: Directory containing TensorBoard logs
        output_dir: Directory to save visualizations
        run_name: Optional name for this training run
        show_plots: Whether to display plots interactively
        save_plots: Whether to save plots to files

    Returns:
        Dictionary with analysis results
    """
    visualizer = LossVisualizer(logs_dir, output_dir)
    return visualizer.visualize_training_loss(run_name, show_plots, save_plots)


def compare_training_runs(run_configs, output_dir="./visualizations"):
    """
    Compare multiple training runs

    Args:
        run_configs: List of dicts with 'name', 'logs_dir' for each run
        output_dir: Directory to save comparison plots

    Returns:
        Comparison analysis results
    """

    print("🔄 Comparing multiple training runs...")

    all_runs_data = {}

    # Load data from each run
    for config in run_configs:
        name = config['name']
        logs_dir = config['logs_dir']

        print(f"   Loading {name}...")
        visualizer = LossVisualizer(logs_dir, output_dir)
        log_dirs = visualizer.find_log_directories()

        run_data = {}
        for log_type, log_files in log_dirs.items():
            combined_data = defaultdict(lambda: {'steps': [], 'values': [], 'wall_times': []})

            for log_file in log_files:
                data = visualizer.parse_tensorboard_logs(log_file)
                for tag, tag_data in data.items():
                    combined_data[tag]['steps'].extend(tag_data['steps'])
                    combined_data[tag]['values'].extend(tag_data['values'])
                    combined_data[tag]['wall_times'].extend(tag_data['wall_times'])

            run_data[log_type] = dict(combined_data)

        all_runs_data[name] = run_data

    # Create comparison plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()

    colors = plt.cm.tab10(np.linspace(0, 1, len(run_configs)))

    # Plot 1: Training loss comparison
    ax1 = axes[0]
    for i, (run_name, run_data) in enumerate(all_runs_data.items()):
        for log_type, data in run_data.items():
            if 'train/loss' in data and data['train/loss']['values']:
                steps = data['train/loss']['steps']
                values = data['train/loss']['values']
                smoothed = LossVisualizer("", "").smooth_curve(values)

                label = f"{run_name}" if log_type == 'main' else f"{run_name}-{log_type}"
                ax1.plot(steps, smoothed, color=colors[i], linewidth=2, label=label)

    ax1.set_xlabel('Steps')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training Loss Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Validation metrics comparison
    ax2 = axes[1]
    for i, (run_name, run_data) in enumerate(all_runs_data.items()):
        for log_type, data in run_data.items():
            if 'eval/span_f1' in data and data['eval/span_f1']['values']:
                steps = data['eval/span_f1']['steps']
                values = data['eval/span_f1']['values']

                label = f"{run_name}" if log_type == 'main' else f"{run_name}-{log_type}"
                ax2.plot(steps, values, color=colors[i], linewidth=2, label=label, marker='o')

    ax2.set_xlabel('Steps')
    ax2.set_ylabel('F1 Score')
    ax2.set_title('Span F1 Score Comparison')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Final metrics comparison
    ax3 = axes[2]
    metrics = ['eval/grammar_f1', 'eval/span_f1', 'eval/relation_f1']
    metric_names = ['Grammar F1', 'Span F1', 'Relation F1']

    x_pos = np.arange(len(metrics))
    width = 0.8 / len(run_configs)

    for i, (run_name, run_data) in enumerate(all_runs_data.items()):
        final_scores = []
        for metric in metrics:
            score = 0
            for log_type, data in run_data.items():
                if metric in data and data[metric]['values']:
                    score = max(score, data[metric]['values'][-1])
            final_scores.append(score)

        ax3.bar(x_pos + i * width, final_scores, width, label=run_name, color=colors[i])

    ax3.set_xlabel('Metrics')
    ax3.set_ylabel('Score')
    ax3.set_title('Final Metrics Comparison')
    ax3.set_xticks(x_pos + width * (len(run_configs) - 1) / 2)
    ax3.set_xticklabels(metric_names)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Plot 4: Training efficiency (steps to convergence)
    ax4 = axes[3]
    run_names = list(all_runs_data.keys())
    convergence_steps = []

    for run_name, run_data in all_runs_data.items():
        min_steps = float('inf')
        for log_type, data in run_data.items():
            if 'train/loss' in data and data['train/loss']['values']:
                # Find step where loss drops below threshold
                values = data['train/loss']['values']
                steps = data['train/loss']['steps']
                threshold = min(values) * 1.1  # 10% above minimum

                for step, loss in zip(steps, values):
                    if loss <= threshold:
                        min_steps = min(min_steps, step)
                        break

        convergence_steps.append(min_steps if min_steps != float('inf') else 0)

    ax4.bar(run_names, convergence_steps, color=colors[:len(run_names)])
    ax4.set_xlabel('Training Runs')
    ax4.set_ylabel('Steps to Convergence')
    ax4.set_title('Training Efficiency Comparison')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save comparison plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(output_dir, f"training_comparison_{timestamp}.png")
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Comparison plot saved to {save_path}")

    plt.show()

    return {
        'runs_data': all_runs_data,
        'comparison_plot': save_path
    }


if __name__ == "__main__":
    # Example usage
    print("🚀 Running Loss Visualization Demo")

    # Visualize current training logs
    results = visualize_training_loss(
        logs_dir="./logs",
        output_dir="./visualizations",
        run_name="demo",
        show_plots=True,
        save_plots=True
    )

    print("\n✅ Visualization complete!")
    if results:
        print(f"Generated {len(results['figures'])} plots")
        print(f"Found {len(results['issues'])} issues")
        print(f"Generated {len(results['recommendations'])} recommendations")
