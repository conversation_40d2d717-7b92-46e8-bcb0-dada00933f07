#!/usr/bin/env python3
"""
验证所有修复是否正确
"""

import json
import os
from my_roberta_entity_relation import load_labels_fromfile

def verify_all_fixes():
    """
    验证所有修复项目
    """
    print("🔍 验证双任务模型修复")
    print("=" * 50)
    
    fixes_status = {}
    
    # 1. 验证数据集文件
    print("1️⃣ 验证数据集文件...")
    dataset_path = "datasets/my_roberta_v2_traindata_entity_relation_only0724.json"
    if os.path.exists(dataset_path):
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        print(f"   ✅ 数据集文件存在，包含 {len(dataset)} 个样本")
        fixes_status['dataset'] = True
    else:
        print(f"   ❌ 数据集文件不存在: {dataset_path}")
        fixes_status['dataset'] = False
    
    # 2. 验证标签文件
    print("\n2️⃣ 验证标签文件...")
    try:
        entity_id_map, relation_id_map = load_labels_fromfile()
        print(f"   ✅ 标签文件加载成功")
        print(f"      • 实体标签: {len(entity_id_map)} 个")
        print(f"      • 关系标签: {len(relation_id_map)} 个")
        fixes_status['labels'] = True
    except Exception as e:
        print(f"   ❌ 标签文件加载失败: {e}")
        fixes_status['labels'] = False
    
    # 3. 验证训练配置
    print("\n3️⃣ 验证训练配置...")
    
    # 计算修复后的配置
    train_split_ratio = 0.8
    train_size = int(len(dataset) * train_split_ratio) if fixes_status['dataset'] else 616
    
    # 修复后的参数
    per_device_train_batch_size = 1
    gradient_accumulation_steps = 4
    num_train_epochs = 15  # 修复后的值
    
    effective_batch_size = per_device_train_batch_size * gradient_accumulation_steps
    steps_per_epoch = train_size // effective_batch_size
    total_steps = steps_per_epoch * num_train_epochs
    
    save_steps = max(200, steps_per_epoch // 2)
    eval_steps = max(100, steps_per_epoch // 3)
    
    print(f"   📊 修复后的训练配置:")
    print(f"      • 训练轮数: {num_train_epochs}")
    print(f"      • 批次大小: {per_device_train_batch_size}")
    print(f"      • 梯度累积: {gradient_accumulation_steps}")
    print(f"      • 有效批次: {effective_batch_size}")
    print(f"      • 每轮步数: {steps_per_epoch}")
    print(f"      • 总训练步数: {total_steps}")
    print(f"      • 评估频率: 每 {eval_steps} 步")
    print(f"      • 保存频率: 每 {save_steps} 步")
    
    # 与原始模型对比
    original_steps = 3465
    training_intensity = total_steps / original_steps
    
    print(f"\n   🔍 与原始模型对比:")
    print(f"      • 原始模型步数: {original_steps}")
    print(f"      • 双任务模型步数: {total_steps}")
    print(f"      • 训练强度比: {training_intensity:.2f}x")
    
    if training_intensity >= 0.8:
        print(f"      ✅ 训练强度充足")
        fixes_status['training_intensity'] = True
    elif training_intensity >= 0.5:
        print(f"      ⚠️ 训练强度中等，可以接受")
        fixes_status['training_intensity'] = True
    else:
        print(f"      ❌ 训练强度不足")
        fixes_status['training_intensity'] = False
    
    # 4. 验证tensor构造修复
    print("\n4️⃣ 验证tensor构造修复...")
    
    # 检查源代码中是否使用了torch.as_tensor
    with open('my_roberta_entity_relation.py', 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    if 'torch.as_tensor' in source_code and 'torch.tensor(' not in source_code:
        print(f"   ✅ Tensor构造已修复，使用torch.as_tensor")
        fixes_status['tensor_construction'] = True
    elif 'torch.tensor(' in source_code:
        print(f"   ⚠️ 仍有torch.tensor使用，建议检查")
        fixes_status['tensor_construction'] = False
    else:
        print(f"   ❓ 未找到tensor构造代码")
        fixes_status['tensor_construction'] = False
    
    # 5. 验证评估策略修复
    print("\n5️⃣ 验证评估策略修复...")
    
    if 'eval_strategy="steps"' in source_code and 'evaluation_strategy' not in source_code:
        print(f"   ✅ 评估策略已修复，使用eval_strategy")
        fixes_status['eval_strategy'] = True
    else:
        print(f"   ❌ 评估策略未正确修复")
        fixes_status['eval_strategy'] = False
    
    # 6. 验证详细日志输出
    print("\n6️⃣ 验证详细日志输出...")
    
    if 'compute_metrics_with_logging' in source_code:
        print(f"   ✅ 已添加详细的评估日志输出")
        fixes_status['detailed_logging'] = True
    else:
        print(f"   ❌ 缺少详细的评估日志输出")
        fixes_status['detailed_logging'] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 修复状态总结:")
    
    total_fixes = len(fixes_status)
    successful_fixes = sum(fixes_status.values())
    
    for fix_name, status in fixes_status.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {fix_name}")
    
    print(f"\n🎯 修复完成度: {successful_fixes}/{total_fixes} ({successful_fixes/total_fixes*100:.1f}%)")
    
    if successful_fixes == total_fixes:
        print("🎉 所有修复都已完成！双任务模型已准备就绪。")
        print("\n💡 建议的下一步:")
        print("   1. 运行完整训练: python my_roberta_entity_relation.py")
        print("   2. 监控训练过程中的评估输出")
        print("   3. 对比训练时长和性能指标")
    elif successful_fixes >= total_fixes * 0.8:
        print("✅ 大部分修复已完成，模型基本可用。")
        print("⚠️ 请检查未完成的修复项目。")
    else:
        print("⚠️ 还有重要的修复项目未完成，建议先解决这些问题。")
    
    return fixes_status

def show_training_comparison():
    """
    显示训练配置对比
    """
    print("\n" + "=" * 60)
    print("📊 训练配置对比表")
    print("=" * 60)
    
    print(f"{'配置项':<20} {'原始四任务模型':<20} {'修复后双任务模型':<20}")
    print("-" * 60)
    print(f"{'批次大小':<20} {'1-4':<20} {'1':<20}")
    print(f"{'梯度累积':<20} {'4-8':<20} {'4':<20}")
    print(f"{'有效批次':<20} {'4-32':<20} {'4':<20}")
    print(f"{'训练轮数':<20} {'3-5':<20} {'15':<20}")
    print(f"{'总训练步数':<20} {'3465':<20} {'2310 (估算)':<20}")
    print(f"{'评估策略':<20} {'steps':<20} {'steps':<20}")
    print(f"{'评估频率':<20} {'动态计算':<20} {'每51步':<20}")
    print(f"{'保存频率':<20} {'500步':<20} {'每77步':<20}")
    print(f"{'预计训练时长':<20} {'16+ 分钟':<20} {'6-10 分钟':<20}")
    print(f"{'详细评估输出':<20} {'是':<20} {'是':<20}")
    print(f"{'Tensor构造':<20} {'标准':<20} {'优化':<20}")

def main():
    """主函数"""
    print("🔧 双任务模型修复验证工具")
    
    # 验证所有修复
    fixes_status = verify_all_fixes()
    
    # 显示对比表
    show_training_comparison()
    
    print(f"\n🎯 验证完成!")

if __name__ == "__main__":
    main()
