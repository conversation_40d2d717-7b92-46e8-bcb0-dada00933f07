#!/usr/bin/env python3
"""
测试已训练的双任务Entity-Relation模型
专门用于测试保存的模型文件
"""

import os
import sys
import torch
import json
from transformers import BertTokenizerFast, BertConfig, AutoModel
from my_roberta_entity_relation import RoBERTaForEntityRelation, inference, load_labels_fromfile

def test_trained_model(model_path=None):
    """
    测试已训练的双任务模型
    """
    # 如果没有指定路径，使用最新的训练模型
    if model_path is None:
        model_path = "./my_models/roberta_entity_relation_20250725_081446"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return None
    
    print(f"🔍 测试模型: {model_path}")
    
    try:
        # 1. 检查模型目录内容
        print("\n📁 模型目录内容:")
        for file in os.listdir(model_path):
            file_path = os.path.join(model_path, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / (1024*1024)  # MB
                print(f"  - {file} ({size:.1f} MB)")
        
        # 2. 加载tokenizer和配置
        print("\n🔧 加载tokenizer和配置...")
        tokenizer = BertTokenizerFast.from_pretrained(model_path)
        config = BertConfig.from_pretrained(model_path)
        print("✅ Tokenizer和配置加载成功")
        
        # 3. 加载标签映射
        print("\n🏷️ 加载标签映射...")
        entity_labels_path = os.path.join(model_path, "entity_labels.json")
        relation_labels_path = os.path.join(model_path, "relation_labels.json")
        
        with open(entity_labels_path, 'r', encoding='utf-8') as f:
            entity_id_map = json.load(f)
        
        with open(relation_labels_path, 'r', encoding='utf-8') as f:
            relation_id_map = json.load(f)
        
        print(f"✅ 标签映射加载成功 - 实体: {len(entity_id_map)}个, 关系: {len(relation_id_map)}个")
        
        # 4. 初始化模型
        print("\n🤖 初始化模型...")
        model = RoBERTaForEntityRelation(config,
                                       entity_label_count=len(entity_id_map),
                                       relation_label_count=len(relation_id_map))
        print(f"✅ 模型初始化成功 - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 5. 尝试加载模型权重
        print("\n💾 加载模型权重...")
        model_loaded = False
        
        # 检查可用的模型文件
        model_files = [
            "model.safetensors",
            "pytorch_model.bin", 
            "model.bin"
        ]
        
        for model_file in model_files:
            model_file_path = os.path.join(model_path, model_file)
            if os.path.exists(model_file_path):
                try:
                    if model_file.endswith('.safetensors'):
                        print(f"  尝试加载 {model_file} (SafeTensors格式)...")
                        # 对于safetensors，我们需要特殊处理
                        # 由于这是自定义模型，直接使用torch加载可能更合适
                        from safetensors.torch import load_file
                        state_dict = load_file(model_file_path)
                        model.load_state_dict(state_dict, strict=False)
                        print(f"✅ 成功加载模型权重: {model_file}")
                        model_loaded = True
                        break
                    else:
                        print(f"  尝试加载 {model_file} (PyTorch格式)...")
                        state_dict = torch.load(model_file_path, map_location='cpu')
                        model.load_state_dict(state_dict)
                        print(f"✅ 成功加载模型权重: {model_file}")
                        model_loaded = True
                        break
                except Exception as e:
                    print(f"  ⚠️ 加载 {model_file} 失败: {e}")
                    continue
        
        if not model_loaded:
            print("⚠️ 警告: 无法加载预训练权重，将使用随机初始化的模型")
        
        # 6. 设置全局变量（为了兼容性）
        import my_roberta_entity_relation
        my_roberta_entity_relation.CONVERSATION_ENTITY_ID_MAP = entity_id_map
        my_roberta_entity_relation.CONVERSATION_RELATION_ID_MAP = relation_id_map
        
        # 7. 准备测试句子
        test_sentences = [
            "项目P001的物料M001需要发货到客户地址",
            "合同C001中包含项目P002的技术支持服务", 
            "项目经理张三负责项目P003的进度管理",
            "请查询项目P-25034574的交付状态",
            "物料编号M-789需要在下周二发货"
        ]
        
        print(f"\n🧪 开始推理测试 ({len(test_sentences)}个句子)...")
        
        # 8. 进行推理测试 (使用较低的阈值)
        model.eval()
        results = inference(model, test_sentences, tokenizer,
                          entity_id_map=entity_id_map,
                          relation_id_map=relation_id_map,
                          start_threshold=0.3,  # 降低阈值
                          end_threshold=0.3)    # 降低阈值
        
        # 9. 显示结果
        print("\n📊 测试结果:")
        print("=" * 80)
        
        total_entities = 0
        total_relations = 0
        
        for i, result in enumerate(results):
            print(f"\n🔸 句子 {i+1}: {result['sentence']}")
            print(f"   实体数量: {len(result['entities'])}")
            print(f"   关系数量: {len(result['relations'])}")
            
            total_entities += len(result['entities'])
            total_relations += len(result['relations'])
            
            # 显示前3个实体
            if result['entities']:
                print("   📍 实体:")
                for j, entity in enumerate(result['entities'][:3]):
                    print(f"      {j+1}. {entity}")
                if len(result['entities']) > 3:
                    print(f"      ... 还有 {len(result['entities'])-3} 个实体")
            
            # 显示前3个关系
            if result['relations']:
                print("   🔗 关系:")
                for j, relation in enumerate(result['relations'][:3]):
                    print(f"      {j+1}. {relation}")
                if len(result['relations']) > 3:
                    print(f"      ... 还有 {len(result['relations'])-3} 个关系")
        
        print("\n" + "=" * 80)
        print(f"📈 总计: {total_entities} 个实体, {total_relations} 个关系")
        print("✅ 模型测试完成!")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 双任务Entity-Relation模型测试工具")
    print("=" * 50)
    
    # 检查是否有命令行参数指定模型路径
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = None
    
    # 运行测试
    results = test_trained_model(model_path)
    
    if results is not None:
        print("\n🎉 测试成功完成!")
    else:
        print("\n💥 测试失败!")

if __name__ == "__main__":
    main()
