#!/usr/bin/env python3
"""
诊断双任务模型的详细脚本
检查模型输出、阈值、权重等
"""

import os
import torch
import json
import numpy as np
from transformers import BertTokenizerFast, BertConfig
from safetensors.torch import load_file
from my_roberta_entity_relation import RoBERTaForEntityRelation

def diagnose_model(model_path="./my_models/roberta_entity_relation_20250725_081446"):
    """
    详细诊断模型
    """
    print(f"🔍 诊断模型: {model_path}")
    
    # 1. 加载模型组件
    tokenizer = BertTokenizerFast.from_pretrained(model_path)
    config = BertConfig.from_pretrained(model_path)
    
    with open(os.path.join(model_path, "entity_labels.json"), 'r', encoding='utf-8') as f:
        entity_id_map = json.load(f)
    
    with open(os.path.join(model_path, "relation_labels.json"), 'r', encoding='utf-8') as f:
        relation_id_map = json.load(f)
    
    # 2. 初始化模型
    model = RoBERTaForEntityRelation(config,
                                   entity_label_count=len(entity_id_map),
                                   relation_label_count=len(relation_id_map))
    
    # 3. 加载权重
    state_dict = load_file(os.path.join(model_path, "model.safetensors"))
    missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
    
    print(f"📊 权重加载统计:")
    print(f"  - 缺失的键: {len(missing_keys)}")
    if missing_keys:
        print(f"    {missing_keys[:5]}...")  # 只显示前5个
    print(f"  - 意外的键: {len(unexpected_keys)}")
    if unexpected_keys:
        print(f"    {unexpected_keys[:5]}...")  # 只显示前5个
    
    # 4. 测试句子
    test_sentence = "项目P001的物料M001需要发货到客户地址"
    print(f"\n🧪 测试句子: {test_sentence}")
    
    # 5. Tokenization
    inputs = tokenizer(
        test_sentence,
        max_length=256,
        padding="max_length",
        truncation=True,
        return_tensors="pt"
    )
    
    print(f"📝 Tokenization结果:")
    print(f"  - input_ids shape: {inputs['input_ids'].shape}")
    print(f"  - tokens: {tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])[:20]}...")
    
    # 6. 模型前向传播
    model.eval()
    with torch.no_grad():
        _, outputs = model(**inputs)
    
    print(f"\n🔮 模型输出:")
    start_logits = outputs["start_logits"]
    end_logits = outputs["end_logits"]
    
    print(f"  - start_logits shape: {start_logits.shape}")
    print(f"  - end_logits shape: {end_logits.shape}")
    
    # 7. 分析logits分布
    start_probs = torch.sigmoid(start_logits).squeeze()
    end_probs = torch.sigmoid(end_logits).squeeze()
    
    print(f"\n📈 概率分布分析:")
    print(f"  - start_probs 范围: [{start_probs.min():.4f}, {start_probs.max():.4f}]")
    print(f"  - end_probs 范围: [{end_probs.min():.4f}, {end_probs.max():.4f}]")
    print(f"  - start_probs 平均值: {start_probs.mean():.4f}")
    print(f"  - end_probs 平均值: {end_probs.mean():.4f}")
    
    # 8. 不同阈值下的检测结果
    thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
    print(f"\n🎯 不同阈值下的实体检测:")
    
    for threshold in thresholds:
        start_positions = (start_probs > threshold).nonzero(as_tuple=True)[0]
        end_positions = (end_probs > threshold).nonzero(as_tuple=True)[0]
        
        print(f"  阈值 {threshold}: start={len(start_positions)}, end={len(end_positions)}")
        
        if len(start_positions) > 0:
            print(f"    start位置: {start_positions[:5].tolist()}...")
            print(f"    start概率: {start_probs[start_positions[:5]].tolist()}")
        
        if len(end_positions) > 0:
            print(f"    end位置: {end_positions[:5].tolist()}...")
            print(f"    end概率: {end_probs[end_positions[:5]].tolist()}")
    
    # 9. 检查最高概率的位置
    print(f"\n🔝 最高概率位置:")
    top_start_indices = torch.topk(start_probs, 5).indices
    top_end_indices = torch.topk(end_probs, 5).indices
    
    print(f"  Top 5 start位置: {top_start_indices.tolist()}")
    print(f"  Top 5 start概率: {start_probs[top_start_indices].tolist()}")
    print(f"  Top 5 end位置: {top_end_indices.tolist()}")
    print(f"  Top 5 end概率: {end_probs[top_end_indices].tolist()}")
    
    # 10. 检查对应的tokens
    tokens = tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
    print(f"\n🔤 对应的tokens:")
    for i, idx in enumerate(top_start_indices[:3]):
        if idx < len(tokens):
            print(f"  start[{idx}] = '{tokens[idx]}' (prob: {start_probs[idx]:.4f})")
    
    for i, idx in enumerate(top_end_indices[:3]):
        if idx < len(tokens):
            print(f"  end[{idx}] = '{tokens[idx]}' (prob: {end_probs[idx]:.4f})")
    
    # 11. 检查模型是否真的训练过
    print(f"\n🧠 模型训练状态检查:")
    
    # 检查分类器权重的分布
    start_classifier_weight = model.start_classifier.weight.data
    end_classifier_weight = model.end_classifier.weight.data
    entity_classifier_weight = model.entity_type_classifier.weight.data
    
    print(f"  - start_classifier权重统计: mean={start_classifier_weight.mean():.6f}, std={start_classifier_weight.std():.6f}")
    print(f"  - end_classifier权重统计: mean={end_classifier_weight.mean():.6f}, std={end_classifier_weight.std():.6f}")
    print(f"  - entity_classifier权重统计: mean={entity_classifier_weight.mean():.6f}, std={entity_classifier_weight.std():.6f}")
    
    # 12. 建议
    print(f"\n💡 诊断建议:")
    
    max_start_prob = start_probs.max().item()
    max_end_prob = end_probs.max().item()
    
    if max_start_prob < 0.1 and max_end_prob < 0.1:
        print("  ⚠️ 所有位置的概率都很低，可能的原因:")
        print("    1. 模型权重没有正确加载")
        print("    2. 模型没有充分训练")
        print("    3. 测试数据与训练数据分布差异很大")
        print("  💡 建议: 检查训练日志，确认模型是否收敛")
    
    elif max_start_prob < 0.5 and max_end_prob < 0.5:
        print("  ⚠️ 概率偏低，建议:")
        print("    1. 降低检测阈值到0.1-0.3")
        print("    2. 检查训练数据的标注质量")
        print("  💡 建议: 使用较低的阈值进行测试")
    
    else:
        print("  ✅ 模型输出正常，建议:")
        print("    1. 调整检测阈值")
        print("    2. 检查推理逻辑")
    
    return {
        'start_probs': start_probs,
        'end_probs': end_probs,
        'tokens': tokens,
        'max_start_prob': max_start_prob,
        'max_end_prob': max_end_prob
    }

def main():
    """主函数"""
    print("🔧 双任务模型诊断工具")
    print("=" * 50)
    
    results = diagnose_model()
    
    print("\n🎯 诊断完成!")
    
    # 基于诊断结果给出具体建议
    if results['max_start_prob'] > 0.1 or results['max_end_prob'] > 0.1:
        print("\n🚀 尝试使用较低阈值重新测试:")
        print("python test_trained_model.py --threshold 0.1")

if __name__ == "__main__":
    main()
