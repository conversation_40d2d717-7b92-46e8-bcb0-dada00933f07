# 分离式训练策略使用指南

## 概述

分离式训练策略是一种真正的参数级别分离训练方法，将多任务RoBERTa模型的训练分为两个独立阶段，每个阶段只训练相关的参数，其他参数被冻结。

## 训练策略对比

### 当前的staged_training（混合训练）
- 通过调整损失权重来强调不同任务
- 所有输出层的参数仍然会同时更新
- 无法实现真正的参数隔离

### 新的separated_training（分离式训练）
- **阶段1：基础任务训练**
  - 只训练grammar和subdomain分类器参数
  - 冻结entity相关和relation分类器参数
  - 可选择是否冻结backbone参数
  
- **阶段2：实体关系任务训练**
  - 冻结已训练好的grammar和subdomain分类器参数
  - 只训练entity相关（start, end, entity_type）和relation分类器参数
  - 可选择是否继续微调backbone参数

## 核心功能

### 1. 参数冻结/解冻机制

```python
# 冻结指定任务组的参数
model.freeze_parameters(['entity', 'relation'])

# 解冻指定任务组的参数
model.unfreeze_parameters(['backbone', 'grammar'])
```

支持的任务组：
- `'backbone'`: RoBERTa backbone
- `'grammar'`: grammar分类器
- `'subdomain'`: subdomain分类器
- `'entity'`: entity相关任务（start, end, entity_type分类器）
- `'relation'`: relation分类器

### 2. 阶段性损失计算

```python
# 设置训练阶段
model.training_stage = 'stage1'  # 只计算grammar和subdomain损失
model.training_stage = 'stage2'  # 只计算entity和relation损失
model.training_stage = None      # 计算所有任务损失
```

### 3. 分离式评估指标

```python
# 阶段性评估指标计算
metrics = model.compute_metrics_separated(eval_pred, training_stage='stage1')
```

## 使用方法

### 1. 通过主菜单使用

运行主程序后选择选项9：
```
9. Train model with separated approach (分离式训练)
```

### 2. 直接调用API

```python
# 调用分离式训练
stage_results = model.separated_training(
    tokenizer=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    stage1_epochs=3,              # 阶段1训练轮数
    stage2_epochs=3,              # 阶段2训练轮数
    stage1_lr=3e-5,              # 阶段1学习率
    stage2_lr=2e-5,              # 阶段2学习率
    save_intermediate=True,       # 保存阶段间模型
    freeze_backbone_stage2=False, # 阶段2是否冻结backbone
    custom_name="my_separated_model"
)
```

## 配置选项

### 训练参数
- **stage1_epochs**: 阶段1训练轮数（推荐2-5轮）
- **stage2_epochs**: 阶段2训练轮数（推荐2-5轮）
- **stage1_lr**: 阶段1学习率（推荐3e-5）
- **stage2_lr**: 阶段2学习率（推荐2e-5，通常比阶段1略低）

### 策略选项
- **freeze_backbone_stage2**: 
  - `False`: 阶段2继续微调backbone（推荐，效果更好）
  - `True`: 阶段2冻结backbone（训练更快，内存占用更少）

- **save_intermediate**: 
  - `True`: 保存阶段1完成后的模型（推荐）
  - `False`: 不保存中间模型

## 输出文件

### 模型保存结构
```
./my_models/
├── {custom_name}_stage1_{timestamp}/    # 阶段1完成后的模型
│   ├── config.json
│   ├── pytorch_model.bin
│   └── tokenizer files...
└── {custom_name}_final_{timestamp}/     # 最终完整模型
    ├── config.json
    ├── pytorch_model.bin
    └── tokenizer files...
```

### 训练日志
```
./logs/
├── stage_1_foundation/     # 阶段1训练日志
└── stage_2_entity_relation/ # 阶段2训练日志
```

## 优势

1. **真正的参数隔离**: 每个阶段只训练相关参数，避免任务间干扰
2. **更稳定的训练**: 分阶段训练减少了多任务学习的复杂性
3. **更好的收敛**: 每个阶段专注于特定任务，容易达到更好的效果
4. **灵活的策略**: 可以根据需要调整每个阶段的训练参数
5. **中间模型保存**: 可以获得专门的基础任务模型

## 适用场景

- 多任务学习中任务间存在干扰
- 希望获得专门的基础任务模型
- 训练资源有限，希望分阶段进行
- 需要更精细的训练控制

## 注意事项

1. **数据要求**: 确保训练数据包含所有任务的标注
2. **内存使用**: 分离式训练通常比混合训练使用更少的内存
3. **训练时间**: 总训练时间可能比混合训练略长
4. **模型兼容性**: 最终模型与原始多任务模型完全兼容

## 故障排除

### 常见问题

1. **参数冻结失效**
   - 检查task_group名称是否正确
   - 确认模型初始化正常

2. **损失计算异常**
   - 检查training_stage设置
   - 确认标签数据完整

3. **内存不足**
   - 减少batch_size
   - 启用gradient_accumulation_steps
   - 考虑冻结backbone

### 调试技巧

```python
# 检查参数状态
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"Trainable: {trainable_params:,}/{total_params:,}")

# 检查训练阶段
print(f"Current training stage: {model.training_stage}")
```

## 性能建议

1. **阶段1**: 使用较高学习率（3e-5），快速学习基础任务
2. **阶段2**: 使用较低学习率（2e-5），精细调整实体关系任务
3. **Backbone**: 阶段2建议继续微调backbone以获得更好效果
4. **Epochs**: 每个阶段2-5轮通常足够，避免过拟合
