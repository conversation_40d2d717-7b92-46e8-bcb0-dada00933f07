#!/usr/bin/env python3
"""
测试训练修复 - 运行一个短暂的训练来验证compute_metrics修复
"""

import json
import os
from my_roberta_entity_relation import train_entity_relation_model

def test_training_with_fixed_compute_metrics():
    """
    运行一个短暂的训练测试来验证compute_metrics修复
    """
    print("🧪 测试训练修复 - compute_metrics问题")
    print("=" * 60)
    
    try:
        # 创建一个非常小的测试数据集
        dataset_path = "datasets/my_roberta_v2_traindata_entity_relation_only0724.json"
        
        if not os.path.exists(dataset_path):
            print(f"❌ 数据集文件不存在: {dataset_path}")
            return False
        
        with open(dataset_path, 'r', encoding='utf-8') as f:
            full_dataset = json.load(f)
        
        # 只取前10个样本进行快速测试
        test_dataset = full_dataset[:10]
        test_dataset_path = "test_tiny_dataset.json"
        
        with open(test_dataset_path, 'w', encoding='utf-8') as f:
            json.dump(test_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建微型测试数据集: {len(test_dataset)} 个样本")
        
        # 运行非常短的训练测试
        print("\n🚀 开始微型训练测试...")
        print("   目标: 验证compute_metrics修复，运行到第一次评估")
        
        try:
            model, tokenizer, output_dir = train_entity_relation_model(
                train_dataset_path=test_dataset_path,
                num_train_epochs=1,  # 只训练1轮
                per_device_train_batch_size=2,  # 稍大的批次
                gradient_accumulation_steps=2,  # 减少梯度累积
                logging_steps=2,    # 非常频繁的日志
                save_steps=6,       # 快速保存 (3的倍数)
                eval_steps=3,       # 快速评估 - 这里会测试compute_metrics
                warmup_ratio=0.0,   # 无预热
                learning_rate=5e-5, # 稍高的学习率
            )
            
            print(f"\n✅ 微型训练测试成功完成!")
            print(f"   模型保存在: {output_dir}")
            print(f"   🎯 关键验证: compute_metrics函数在评估时正常工作")
            
            # 清理测试文件
            if os.path.exists(test_dataset_path):
                os.remove(test_dataset_path)
            
            # 清理测试模型文件（可选）
            import shutil
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir)
                print(f"   🧹 清理测试模型文件: {output_dir}")
            
            return True
            
        except Exception as training_error:
            print(f"❌ 训练过程中出现错误: {training_error}")
            
            # 检查是否是compute_metrics相关的错误
            error_str = str(training_error)
            if "compute_metrics" in error_str:
                print(f"🔍 这是compute_metrics相关的错误，需要进一步修复")
            elif "has no attribute" in error_str:
                print(f"🔍 这是属性访问错误，可能与方法调用有关")
            else:
                print(f"🔍 这是其他类型的错误")
            
            import traceback
            traceback.print_exc()
            
            # 清理测试文件
            if os.path.exists(test_dataset_path):
                os.remove(test_dataset_path)
            
            return False
        
    except Exception as e:
        print(f"❌ 测试准备失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_training_configuration():
    """
    验证训练配置是否正确
    """
    print(f"\n🔍 验证训练配置")
    print("=" * 40)
    
    try:
        # 检查数据集
        dataset_path = "datasets/my_roberta_v2_traindata_entity_relation_only0724.json"
        if os.path.exists(dataset_path):
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            print(f"✅ 主数据集: {len(dataset)} 个样本")
        else:
            print(f"❌ 主数据集不存在")
            return False
        
        # 检查标签文件
        entity_labels_file = "datasets/my_roberta_entity_labels.json"
        relation_labels_file = "datasets/my_roberta_relation_labels.json"
        
        if os.path.exists(entity_labels_file) and os.path.exists(relation_labels_file):
            print(f"✅ 标签文件存在")
        else:
            print(f"❌ 标签文件缺失")
            return False
        
        # 检查模型路径
        model_path = "dl_models/hfl/chinese-roberta-wwm-ext"
        if os.path.exists(model_path):
            print(f"✅ 预训练模型路径存在")
        else:
            print(f"⚠️ 预训练模型路径不存在，将尝试下载")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 训练修复验证工具")
    print("🎯 专门测试compute_metrics修复是否解决了训练中断问题")
    print("=" * 70)
    
    # 1. 验证配置
    config_ok = verify_training_configuration()
    
    if not config_ok:
        print("❌ 配置验证失败，无法进行训练测试")
        return
    
    # 2. 询问是否运行训练测试
    print(f"\n❓ 是否运行微型训练测试？")
    print("   这将运行一个非常短的训练来验证compute_metrics修复")
    print("   预计耗时: 1-2分钟")
    choice = input("请输入 y/n: ").strip().lower()
    
    if choice != 'y':
        print("⏭️ 跳过训练测试")
        print("\n💡 如果您确信修复正确，可以直接运行完整训练:")
        print("   python my_roberta_entity_relation.py")
        return
    
    # 3. 运行训练测试
    test_passed = test_training_with_fixed_compute_metrics()
    
    # 4. 总结
    print(f"\n" + "=" * 70)
    print("📋 测试结果总结:")
    
    if test_passed:
        print("🎉 训练修复测试成功!")
        print("\n✅ 验证结果:")
        print("   • compute_metrics函数调用正常")
        print("   • 评估过程无错误")
        print("   • 训练可以正常进行到评估阶段")
        
        print(f"\n🚀 现在可以安全地运行完整训练:")
        print("   python my_roberta_entity_relation.py")
        
        print(f"\n📊 预期的训练行为:")
        print("   • 训练将运行15轮，共2310步")
        print("   • 每100步进行一次评估并显示指标")
        print("   • 每200步保存一次模型")
        print("   • 预计训练时长: 6-10分钟")
        
    else:
        print("❌ 训练修复测试失败")
        print("\n🔍 可能的问题:")
        print("   • compute_metrics函数仍有问题")
        print("   • 其他训练配置问题")
        print("   • 数据预处理问题")
        
        print(f"\n💡 建议:")
        print("   1. 检查错误日志中的具体错误信息")
        print("   2. 确认所有依赖包版本正确")
        print("   3. 检查数据集格式是否正确")

if __name__ == "__main__":
    main()
