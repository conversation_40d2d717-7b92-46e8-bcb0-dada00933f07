
# get commandline input from user
import sys
import my_model_func as mmf
import my_dataset_func as mdf
import colorama

# define global variables
model = None
tokenizer = None
train_dataset = None
eval_dataset = None
dataset_file_path = "./datasets"

loop = True
while loop:

    # print user input options
    print("Enter the number of the option you would like to run:")
    print("0. Exit.")
    print("1. Download model from hugging face.")
    print("2. Load model from local path.")
    print("3. Train with pre-defined dataset.")
    print("4. Save model to local path.")
    print("5. Eval with pre-defined dataset.")
    print("6. Generate train dataset.")
    print("Choice:", end=" ")

    # get user input
    option = input()
    match option:
        case "0":
            print("Exiting...")
            sys.exit()
        case "1":
            model_name = mmf.input_model_path("Enter the remote model path want to download:")
            mmf.download_model_from_huggingface(model_name)
        case "2":
            model_name = mmf.input_model_path("Enter the local modelpath want to load:")
            model, tokenizer = mmf.load_model_from_local_path(model_name)
        case "3":
            # train dataset stream, eval dataset load one time
            train_row_count, train_dataset = mdf.load_dataset_from_path("train", dataset_file_path, stream_mode=True)
            eval_row_count, eval_dataset = mdf.load_dataset_from_path("eval", dataset_file_path, stream_mode=False)
            print(colorama.Fore.GREEN + f"Train row count: {train_row_count}, Eval row count: {eval_row_count}" + colorama.Style.RESET_ALL)
            mmf.train_with_specific_dataset(model, tokenizer, train_dataset, eval_dataset, train_row_count, custom_epoch=3)
        case "4":
            print("Saving model to local path...")
        case "5":
            eval_row_count, eval_dataset = mdf.load_dataset_from_path("test", dataset_file_path, stream_mode=False)
            mmf.eval_with_specific_dataset(model, tokenizer, eval_dataset)
        case "6":
            dataset_type, dataset_name = mdf.input_generate_dataset_path("Enter the dataset type and name you would like to generate:")  
            mdf.generate_dataset(dataset_type, dataset_name)
        case _:
            print("Invalid option. Exiting...")
            sys.exit()
