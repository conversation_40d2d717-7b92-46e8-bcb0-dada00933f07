#!/usr/bin/env python3
"""
数据集分析脚本
分析my_roberta_v2_traindata_0711_1101_ALL_negative_noStatistic.json数据集
统计各任务的数据分布和特征
"""

import json
from collections import Counter, defaultdict
import statistics

def load_dataset(file_path):
    """加载数据集"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def analyze_grammar_labels(data):
    """分析grammar标签分布"""
    print("=== Grammar标签分析 ===")
    
    # 统计所有grammar标签
    all_grammar_labels = []
    for item in data:
        all_grammar_labels.extend(item['grammar_labels'])
    
    grammar_counter = Counter(all_grammar_labels)
    print(f"Grammar标签总数: {len(all_grammar_labels)}")
    print(f"唯一Grammar标签: {len(grammar_counter)}")
    print("Grammar标签分布:")
    for label, count in grammar_counter.most_common():
        print(f"  {label}: {count} ({count/len(all_grammar_labels)*100:.1f}%)")
    
    # 统计多标签情况
    multi_label_count = sum(1 for item in data if len(item['grammar_labels']) > 1)
    print(f"多标签样本数: {multi_label_count} ({multi_label_count/len(data)*100:.1f}%)")
    
    return grammar_counter

def analyze_subdomain_labels(data):
    """分析subdomain标签分布"""
    print("\n=== Subdomain标签分析 ===")
    
    subdomain_labels = [item['subdomain_label'] for item in data]
    subdomain_counter = Counter(subdomain_labels)
    
    print(f"Subdomain标签总数: {len(subdomain_labels)}")
    print(f"唯一Subdomain标签: {len(subdomain_counter)}")
    print("Subdomain标签分布:")
    for label, count in subdomain_counter.most_common():
        print(f"  {label}: {count} ({count/len(subdomain_labels)*100:.1f}%)")
    
    return subdomain_counter

def analyze_entity_spans(data):
    """分析entity spans分布"""
    print("\n=== Entity Spans分析 ===")
    
    # 统计实体类型
    all_entity_types = []
    entity_counts_per_sample = []
    
    for item in data:
        entities = item['entity_spans']
        entity_counts_per_sample.append(len(entities))
        for entity in entities:
            all_entity_types.append(entity['type'])
    
    entity_type_counter = Counter(all_entity_types)
    
    print(f"实体总数: {len(all_entity_types)}")
    print(f"唯一实体类型: {len(entity_type_counter)}")
    print(f"平均每个样本实体数: {statistics.mean(entity_counts_per_sample):.2f}")
    print(f"最大实体数: {max(entity_counts_per_sample)}")
    print(f"无实体样本数: {entity_counts_per_sample.count(0)}")
    
    print("Top 10 实体类型:")
    for entity_type, count in entity_type_counter.most_common(10):
        print(f"  {entity_type}: {count} ({count/len(all_entity_types)*100:.1f}%)")
    
    return entity_type_counter, entity_counts_per_sample

def analyze_relation_pairs(data):
    """分析relation pairs分布"""
    print("\n=== Relation Pairs分析 ===")
    
    # 统计关系类型
    all_relation_types = []
    relation_counts_per_sample = []
    
    for item in data:
        relations = item['relation_pairs']
        relation_counts_per_sample.append(len(relations))
        for relation in relations:
            all_relation_types.append(relation['type'])
    
    relation_type_counter = Counter(all_relation_types)
    
    print(f"关系总数: {len(all_relation_types)}")
    print(f"唯一关系类型: {len(relation_type_counter)}")
    print(f"平均每个样本关系数: {statistics.mean(relation_counts_per_sample):.2f}")
    print(f"最大关系数: {max(relation_counts_per_sample)}")
    print(f"无关系样本数: {relation_counts_per_sample.count(0)}")
    
    print("关系类型分布:")
    for relation_type, count in relation_type_counter.most_common():
        print(f"  {relation_type}: {count} ({count/len(all_relation_types)*100:.1f}%)")
    
    return relation_type_counter, relation_counts_per_sample

def analyze_task_combinations(data):
    """分析任务组合情况"""
    print("\n=== 任务组合分析 ===")
    
    # 统计各种任务组合
    combinations = defaultdict(int)
    
    for item in data:
        has_entities = len(item['entity_spans']) > 0
        has_relations = len(item['relation_pairs']) > 0
        
        if has_entities and has_relations:
            combinations['entity+relation'] += 1
        elif has_entities and not has_relations:
            combinations['entity_only'] += 1
        elif not has_entities and has_relations:
            combinations['relation_only'] += 1
        else:
            combinations['neither'] += 1
    
    total = len(data)
    print("任务组合分布:")
    for combo, count in combinations.items():
        print(f"  {combo}: {count} ({count/total*100:.1f}%)")
    
    return combinations

def filter_entity_relation_data(data):
    """过滤出包含entity或relation标注的数据"""
    filtered_data = []
    
    for item in data:
        has_entities = len(item['entity_spans']) > 0
        has_relations = len(item['relation_pairs']) > 0
        
        if has_entities or has_relations:
            filtered_data.append(item)
    
    print(f"\n=== 过滤结果 ===")
    print(f"原始数据量: {len(data)}")
    print(f"过滤后数据量: {len(filtered_data)}")
    print(f"保留比例: {len(filtered_data)/len(data)*100:.1f}%")
    
    return filtered_data

def main():
    """主函数"""
    print("开始分析数据集...")
    
    # 加载数据集
    data = load_dataset("datasets/my_roberta_v2_traindata_0724_ALL_negative_noStatistic.json")
    print(f"数据集总样本数: {len(data)}")
    
    # 分析各个任务
    grammar_stats = analyze_grammar_labels(data)
    subdomain_stats = analyze_subdomain_labels(data)
    entity_stats, entity_counts = analyze_entity_spans(data)
    relation_stats, relation_counts = analyze_relation_pairs(data)
    
    # 分析任务组合
    combinations = analyze_task_combinations(data)
    
    # 过滤数据
    filtered_data = filter_entity_relation_data(data)
    
    # 保存过滤后的数据
    output_file = "datasets/my_roberta_v2_traindata_entity_relation_only.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(filtered_data, f, ensure_ascii=False, indent=2)
    print(f"过滤后的数据已保存到: {output_file}")

if __name__ == "__main__":
    main()
