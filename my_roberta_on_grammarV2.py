# Simplified imports for grammar-only model
from sklearn.metrics import precision_recall_fscore_support
# Removed unused imports:
# from sklearn.metrics import accuracy_score, f1_score
import torch
import torch.nn as nn
from transformers import BertModel, Bert<PERSON>onfig, Bert<PERSON>okenizer, BertTokenizerFast, BertPreTrainedModel, BertForQuestionAnswering
from transformers import Trainer, TrainingArguments
from transformers import default_data_collator
import json
import os,sys
import numpy as np
from simple_term_menu import TerminalMenu

# 当前根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
# 添加根目录到系统路径
sys.path.append(ROOT_DIR)
from datasets import load_dataset, Dataset, concatenate_datasets

class RoBERTaForConversationClassification(BertPreTrainedModel):
    """
    RoBERTa model for grammar classification only.
    Simplified from multi-task model to focus solely on grammar classification.

    Removed components:
    - MAX_ENTITY_SENTENCE, MAX_RELATION_SENTENCE, MAX_TOKENS_PER_ENTITY constants
    - question_subdomain_classifier, start_classifier, end_classifier, entity_type_classifier, relation_classifier
    - Multi-task loss weights (weight_subdomain, weight_position, weight_entity_type, weight_relation)
    - Conversation history and project state tracking
    """

    # when init from scratch, means load base roberta from file
    def __init__(self, config, grammar_label_count=None):
        # call parent init
        super().__init__(config)
        # core backbone model
        # did not need load config mannually, cause it will automatically load config from pretrained_model_path
        self.config = config
        self.bert = BertModel(config)
        classifier_dropout = (
            config.classifier_dropout if config.classifier_dropout is not None else config.hidden_dropout_prob
        )
        self.dropout = nn.Dropout(classifier_dropout)

        # Determine grammar label count
        if grammar_label_count is not None:
            num_grammar_labels = grammar_label_count
        else:
            # Try to get from global variable if available
            try:
                num_grammar_labels = len(CONVERSATION_GRAMMAR_ID_MAP)
            except NameError:
                # Default to 3 for the new grammar-only model
                num_grammar_labels = 3
                print(f"Warning: CONVERSATION_GRAMMAR_ID_MAP not defined, using default grammar label count: {num_grammar_labels}")

        # Only grammar classifier - multi-label, because a question can have judge and reason at same time
        self.question_grammar_classifier = nn.Linear(config.hidden_size, num_grammar_labels)

        # Removed classifiers:
        # - self.question_subdomain_classifier (subdomain classification)
        # - self.start_classifier, self.end_classifier (entity position detection)
        # - self.entity_type_classifier (entity type classification)
        # - self.relation_classifier (relation classification)

        # Initialize loss weight for grammar only (removed multi-task weights)
        self.weight_grammar = 1.0
        # Removed weights:
        # - self.weight_subdomain = 0.1
        # - self.weight_position = 3.0
        # - self.weight_entity_type = 1.0
        # - self.weight_relation = 0.5

        # Removed conversation tracking components:
        # - self.conversation_history = []
        # - self.conversation_history_size = 3*3
        # - self.project_state = {}
        # - self.project_state_size = 3

        # Initialize weights and apply final processing
        self.post_init()


    def forward(self,
                input_ids=None,
                attention_mask=None,
                token_type_ids=None,
                grammar_labels=None,
                # Removed parameters for multi-task components:
                # subdomain_labels=None,
                # entity_start_labels=None, entity_end_labels=None,
                # span_entity_labels=None,  # [(start, end, label)] per batch
                # entity_relation_labels=None,
                **kwargs):
        """
        Forward pass for grammar classification only.

        Removed functionality:
        - Subdomain classification
        - Entity start/end position detection
        - Entity type classification
        - Relation classification
        """

        batch_size = input_ids.size(0) if input_ids is not None else 1

        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        sequence_output = outputs.last_hidden_state # [batch, seq_len, hidden]

        # Grammar classification using CLS token
        cls_token_output = outputs.last_hidden_state[:, 0, :]
        grammar_logits = self.question_grammar_classifier(cls_token_output)

        # Removed multi-task logits computation:
        # subdomain_logits = self.question_subdomain_classifier(cls_token_output)
        # start_logits = self.start_classifier(sequence_output).squeeze(-1)
        # end_logits = self.end_classifier(sequence_output).squeeze(-1)

        loss_dict = {}
        # Only grammar loss computation with proper tensor handling
        if grammar_labels is not None:
            # Ensure grammar_labels is a proper tensor
            if not isinstance(grammar_labels, torch.Tensor):
                grammar_labels = torch.as_tensor(grammar_labels)

            # Move to same device as grammar_logits
            grammar_labels = grammar_labels.to(grammar_logits.device)
            # Updated class_weights to match the new 3-label schema
            class_weights = torch.tensor([
                # 0.5,    # query (index 0) - reduce weight for majority class
                # 12.0,   # judge (index 1) - boost minority class
                # 8.0     # reason (index 2) - boost minority class
                1.0, 1.0, 1.0
            ], device=grammar_logits.device)

            # Apply weighted BCEWithLogitsLoss
            grammar_loss = torch.nn.BCEWithLogitsLoss(pos_weight=class_weights)(
                grammar_logits, grammar_labels.float()
            )
            # Ensure proper shape and type for BCEWithLogitsLoss
            # grammar_loss = torch.nn.BCEWithLogitsLoss()(grammar_logits, grammar_labels.float())
            loss_dict["grammar_loss"] = grammar_loss

        # Removed multi-task loss computations:
        # subdomain_loss, position_loss, entity_type_loss, relation_loss

        # Removed entity type loss computation and span processing:
        # All span entity type loss, span_logits_list, and related processing has been removed
        # as this model now focuses only on grammar classification

        # Removed relation loss computation:
        # All relation processing has been removed as this model focuses only on grammar classification

        # Simplified loss computation - only grammar loss
        total_loss = self.weight_grammar * loss_dict.get('grammar_loss', 0.0)

        # FIXED: Return format compatible with Hugging Face trainer
        # The trainer expects either:
        # 1. Just logits (for inference)
        # 2. (loss, logits) tuple (for training)
        # 3. A dict-like object with 'loss' and 'logits' attributes

        if grammar_labels is not None:
            # Training mode: return (loss, logits)
            return total_loss, grammar_logits
        else:
            # Inference mode: return just logits
            return grammar_logits

        # Removed complex dictionary return that was causing compute_metrics issues:
        # return total_loss, {
        #     "grammar_logits": grammar_logits,
        #     "sequence_output": sequence_output
        # }

    @staticmethod
    def compute_metrics(eval_pred, grammar_threshold=0.5):
        """
        Fixed compute_metrics for grammar classification only.

        CRITICAL FIX: Hugging Face trainer passes preds as the raw model output,
        not as a dictionary. For our grammar-only model, preds should be the
        grammar_logits tensor directly.

        Removed metrics:
        - subdomain_acc (subdomain accuracy)
        - start_end_f1 (entity position F1)
        - span_f1 (entity type F1)
        - relation_f1 (relation F1)
        """

        preds, labels = eval_pred

        # # Debug: Print the actual structure of preds and labels
        # print(f"DEBUG - preds type: {type(preds)}")
        # print(f"DEBUG - labels type: {type(labels)}")
        # if hasattr(preds, 'shape'):
        #     print(f"DEBUG - preds shape: {preds.shape}")
        # if hasattr(labels, 'shape'):
        #     print(f"DEBUG - labels shape: {labels.shape}")

        # FIXED: Handle the actual trainer output format
        # The trainer passes predictions as raw tensors, not dictionaries
        if isinstance(preds, dict):
            # If somehow preds is still a dict (shouldn't happen with fixed forward)
            pred_grammar_logits = preds['grammar_logits']
        elif isinstance(preds, (tuple, list)):
            # If preds is a tuple/list, grammar_logits should be the first element
            pred_grammar_logits = preds[0] if len(preds) > 0 else preds
        else:
            # Direct tensor case - this should be the normal case
            pred_grammar_logits = preds

        # Fixed labels processing for grammar-only model
        if isinstance(labels, dict):
            true_grammar = labels['grammar_labels']
        elif isinstance(labels, (list, tuple)):
            # For grammar-only model, labels should be a single tensor
            true_grammar = labels[0] if len(labels) > 0 else labels
        else:
            # Direct tensor case
            true_grammar = labels

        # Ensure tensors are properly formatted
        pred_grammar_tensor = torch.as_tensor(pred_grammar_logits)
        true_grammar_tensor = torch.as_tensor(true_grammar)

        # print(f"DEBUG - pred_grammar_tensor shape: {pred_grammar_tensor.shape}")
        # print(f"DEBUG - true_grammar_tensor shape: {true_grammar_tensor.shape}")

        # Apply sigmoid and threshold for predictions
        pred_grammar = (torch.sigmoid(pred_grammar_tensor) > grammar_threshold).int().numpy()
        true_grammar_np = true_grammar_tensor.int().numpy()

        # print(f"DEBUG - pred_grammar shape: {pred_grammar.shape}")
        # print(f"DEBUG - true_grammar_np shape: {true_grammar_np.shape}")
        # print(f"DEBUG - Sample predictions: {pred_grammar[:3] if len(pred_grammar) > 0 else 'empty'}")
        # print(f"DEBUG - Sample true labels: {true_grammar_np[:3] if len(true_grammar_np) > 0 else 'empty'}")

        # Calculate F1 score with proper error handling
        try:
            grammar_f1, _ = precision_recall_fscore_support(
                true_grammar_np, pred_grammar,
                average='samples',
                zero_division=0
            )[:2]
            # print(f"DEBUG - Calculated grammar_f1: {grammar_f1}")
        except Exception as e:
            print(f"ERROR: Error calculating grammar F1: {e}")
            print(f"ERROR - pred_grammar shape: {pred_grammar.shape}")
            print(f"ERROR - true_grammar_np shape: {true_grammar_np.shape}")
            import traceback
            traceback.print_exc()
            grammar_f1 = 0.0

        return {
            'grammar_f1': grammar_f1
        }
        
    def inference(self, sentences, tokenizer, max_length=256, padding="max_length", truncation=True, grammar_threshold=0.5, grammar_id_array=None):
        """
        Simplified inference for grammar classification only.

        Args:
            sentences: Input sentences
            tokenizer: Tokenizer to use
            max_length: Maximum sequence length
            padding: Padding strategy
            truncation: Whether to truncate
            grammar_threshold: Threshold for classification
            grammar_id_array: Optional array of grammar labels. If None, will try to use global variable.

        Removed functionality:
        - Entity extraction (start/end position detection, entity type classification)
        - Relation extraction
        - Subdomain classification
        """

        # Get grammar label array
        if grammar_id_array is not None:
            id_array = grammar_id_array
        else:
            try:
                id_array = CONVERSATION_ID_GRAMMAR_ARY
            except NameError:
                # Default array for 3-label system
                id_array = ["query", "judge", "reason"]
                print(f"Warning: Using default grammar label array: {id_array}")

        # tokenize sentences
        inputs = tokenizer(sentences, padding=padding, truncation=truncation, max_length=max_length, return_tensors="pt")

        # move to gpu if available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.to(device)
        inputs = {key: value.to(device) for key, value in inputs.items()}

        # disable gradient for inference
        with torch.no_grad():
            # FIXED: Handle the new forward return format
            grammar_logits = self.forward(
                input_ids=inputs.get("input_ids"),
                attention_mask=inputs.get("attention_mask"),
                token_type_ids=inputs.get("token_type_ids", None)  # Optional argument
            )
            # Note: In inference mode (no labels), forward now returns just grammar_logits

        # Removed multi-task outputs access:
        # grammar_logits = outputs["grammar_logits"]
        # subdomain_logits = outputs["subdomain_logits"]
        # start_logits = outputs["start_logits"]
        # end_logits = outputs["end_logits"]

        results = []
        for b_idx, sentence in enumerate(sentences):
            # Grammar classification multi-label
            grammar_pred = (torch.sigmoid(grammar_logits[b_idx]) > grammar_threshold).int()
            grammar_intent_label = [id_array[i] for i, val in enumerate(grammar_pred) if val == 1]

            # Removed multi-task processing:
            # - subdomain classification
            # - entity extraction (span detection and type classification)
            # - relation extraction

            results.append({
                "sentence": sentence,
                "grammar_labels": grammar_intent_label
                # Removed outputs:
                # "subdomain": subdomain_intent_label,
                # "entities": [...],
                # "relations": [...]
            })

        return results


    # prepare dataset for training
    # sample data format
    """
    {
        "sentence": "项目 XT-20250101-1234 的负责人是谁？",
        "grammar_labels": ["Query", "Reason"],  // multi-label
        "subdomain_label": "BaseInfo",          // single-label
        "entity_spans": [
            { "start": 3, "end": 16, "type": "B-PROJID" },  // char positions
            { "start": 17, "end": 20, "type": "B-ROLE-MGR" }
        ],
        "relation_pairs": [
            { "start_i": 3, "end_i": 16, "start_j": 17, "end_j": 20, "type": "Has_Property" }
        ]
    }
    """
    def preprocess_dataset(dataset, tokenizer, max_length=256, padding="max_length", truncation=True, grammar_id_map=None):
        """
        Simplified preprocess_dataset for grammar classification only.

        Expected input format for grammar-only dataset:
        {
            "sentence": "那个G-20240518项目，屏柜数量是多少来着？",
            "grammar_labels": ["query"]
        }

        Args:
            dataset: Input dataset
            tokenizer: Tokenizer to use
            max_length: Maximum sequence length
            padding: Padding strategy
            truncation: Whether to truncate
            grammar_id_map: Optional grammar label to ID mapping. If None, will try to use global variable.

        Removed processing for:
        - subdomain_label
        - entity_spans
        - relation_pairs
        """

        # Get grammar label mapping
        if grammar_id_map is not None:
            label_map = grammar_id_map
        else:
            try:
                label_map = CONVERSATION_GRAMMAR_ID_MAP
            except NameError:
                # Default mapping for 3-label system
                label_map = {"query": 1, "judge": 2, "reason": 3}
                print(f"Warning: Using default grammar label mapping: {label_map}")
        # Handle both batched and non-batched inputs
        batched = isinstance(dataset["sentence"], list)

        # For non-batched case, convert to batch of size 1
        if not batched:
            batch_sentences = [dataset["sentence"]]
            batch_grammar_labels = [dataset["grammar_labels"]]
            # Removed multi-task data extraction:
            # batch_subdomain_label = [dataset["subdomain_label"]]
            # batch_entity_spans = [dataset["entity_spans"]]
            # batch_relation_pairs = [dataset["relation_pairs"]]
        else:
            batch_sentences = dataset["sentence"]
            batch_grammar_labels = dataset["grammar_labels"]
            # Removed multi-task data extraction:
            # batch_subdomain_label = dataset["subdomain_label"]
            # batch_entity_spans = dataset["entity_spans"]
            # batch_relation_pairs = dataset["relation_pairs"]

        if not batch_sentences:
            return {}

        # Tokenize the sentences (removed offset_mapping as not needed for grammar only)
        encodings = tokenizer(batch_sentences, padding=padding, truncation=truncation,
                             max_length=max_length)

        batch_input_ids = encodings['input_ids']
        batch_attention_mask = encodings['attention_mask']

        batch_size = len(batch_sentences)

        grammar_labels_batch = []
        # Removed multi-task label batches:
        # subdomain_labels_batch = []
        # entity_start_labels_batch = []
        # entity_end_labels_batch = []
        # span_entity_labels_batch = []
        # entity_relation_labels_batch = []
        # relation_labels_batch = []

        for i in range(batch_size):
            sentence = batch_sentences[i]

            # Multi-hot grammar labels processing
            grammar_vector = torch.zeros(len(label_map))
            valid_labels = 0
            for label in batch_grammar_labels[i]:
                grammar_id = label_map.get(label, 0)
                if grammar_id > 0 and grammar_id <= len(grammar_vector):
                    grammar_vector[grammar_id-1] = 1  ### 注意检查——期望标签ID与实际ID是否能对应上
                    valid_labels += 1

            # # 如果没有有效标签，设置默认标签（根据句子特征推断）
            # if valid_labels == 0:
            #     sentence_text = batch_sentences[i].lower()
            #     if '?' in sentence_text or '吗' in sentence_text:
            #         # query标签在新的3标签系统中的ID
            #         query_id = label_map.get("query", 1)
            #         if query_id <= len(grammar_vector):
            #             grammar_vector[query_id - 1] = 1
            #     elif '是否' in sentence_text or '有没有' in sentence_text:
            #         # judge标签在新的3标签系统中的ID
            #         judge_id = label_map.get("judge", 2)
            #         if judge_id <= len(grammar_vector):
            #             grammar_vector[judge_id - 1] = 1
            #     elif '为什么' in sentence_text or '原因' in sentence_text:
            #         # reason标签在新的3标签系统中的ID
            #         reason_id = label_map.get("reason", 3)
            #         if reason_id <= len(grammar_vector):
            #             grammar_vector[reason_id - 1] = 1
            #     else:
            #         # 默认为查询
            #         query_id = label_map.get("query", 1)
            #         if query_id <= len(grammar_vector):
            #             grammar_vector[query_id - 1] = 1

            grammar_labels_batch.append(grammar_vector)

            # Removed multi-task processing:
            # - subdomain label processing
            # - entity spans and start/end labels processing
            # - relation pairs processing
            # - padding for entity and relation labels

        # print batch size
        print("batch size: ", len(batch_sentences))
        print("mapped sentences: \n", "\n".join(batch_sentences[:10]), "\n..." if len(batch_sentences) > 10 else "")

        # Simplified return - only grammar-related data
        # Column name must end with _labels for trainer compatibility
        # Convert grammar_labels_batch to proper tensor format
        if not batched:
            return {
                'input_ids': batch_input_ids[0],
                'attention_mask': batch_attention_mask[0],
                'grammar_labels': grammar_labels_batch[0]
                # Removed multi-task labels:
                # 'subdomain_labels': subdomain_labels_batch[0],
                # 'entity_start_labels': entity_start_labels_batch[0],
                # 'entity_end_labels': entity_end_labels_batch[0],
                # 'span_entity_labels': span_entity_labels_batch[0],
                # 'entity_relation_labels': entity_relation_labels_batch[0]
            }
        else:
            # Stack grammar labels into a proper tensor for batched processing
            grammar_labels_tensor = torch.stack(grammar_labels_batch) if grammar_labels_batch else torch.empty(0)
            return {
                'input_ids': batch_input_ids,
                'attention_mask': batch_attention_mask,
                'grammar_labels': grammar_labels_tensor
                # Removed multi-task labels:
                # 'subdomain_labels': subdomain_labels_batch,
                # 'entity_start_labels': entity_start_labels_batch,
                # 'entity_end_labels': entity_end_labels_batch,
                # 'span_entity_labels': span_entity_labels_batch,
                # 'entity_relation_labels': entity_relation_labels_batch
            }

    # when train datset if load in stream mode, max_row_count must be specified
    def custom_train(self, tokenizer, train_dataset, eval_dataset, max_row_count=None, custom_epoch=3, save_steps=100):

        if max_row_count is None:
            max_row_count = len(train_dataset)

        print("train dataset size: ", max_row_count)
        print("eval dataset size: ", len(eval_dataset) if eval_dataset else 0)
        print("train dataset: ", train_dataset)
        print("eval dataset: ", eval_dataset)

        tokenized_train_dataset = train_dataset.map(lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer), batched=True)
        # convert to torch tensor
        tokenized_train_dataset.set_format(type='torch')
        if eval_dataset is not None:
            tokenized_eval_dataset = eval_dataset.map(lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer), batched=True)
            # convert to torch tensor
            tokenized_eval_dataset.set_format(type='torch')
            print(tokenized_eval_dataset[0].keys())
        else:
            tokenized_eval_dataset = None

        training_args = TrainingArguments(
            max_steps=max_row_count*custom_epoch,
            output_dir="./results",             # 保存模型的目录
            overwrite_output_dir=True,          # 是否覆盖输出目录
            save_strategy="no",                 # 保存策略
            save_steps=save_steps,              # 每1000步保存一次
            save_total_limit=1,                 # 限制保存的模型数量
            eval_strategy="steps" if eval_dataset else "no",              # 每个epoch后进行评估
            eval_steps=max_row_count // 4,      # More frequent evaluation for better monitoring
            per_device_train_batch_size=4,      # Increased batch size for better gradient estimates
            per_device_eval_batch_size=8,       # 评估时的批量大小
            gradient_accumulation_steps=2,      # Reduced accumulation steps with larger batch size
            num_train_epochs=1,                 # 训练的轮数
            logging_dir='./logs',               # 日志目录
            logging_steps=50,                  # 每50步输出日志
            learning_rate=5e-5,                 # Higher learning rate for better convergence
            warmup_ratio=0.1,                   # Increased warmup for stable training
            lr_scheduler_type="linear",         # Linear decay instead of cosine for better control
            weight_decay=0.01,                  # Add weight decay for regularization
            report_to="tensorboard"             # 输出到tensorboard
            #learning_rate=2e-5,                # 学习率
        )
        trainer = Trainer(
            model=self,
            args=training_args,
            train_dataset=tokenized_train_dataset,
            eval_dataset=tokenized_eval_dataset if tokenized_eval_dataset else None,
            # no need to use custom data collator, cause we already padding to fixed size
            #data_collator=self.custom_data_collator,
            processing_class=tokenizer,  
            compute_metrics=RoBERTaForConversationClassification.compute_metrics if tokenized_eval_dataset else None  # Removed eval_dataset parameter
        )

        trainer.train()
        print("train done.")

    def update_loss_weights(self, weight_grammar):
        """
        Simplified update_loss_weights for grammar classification only.

        Removed parameters:
        - weight_subdomain
        - weight_position
        - weight_entity_type
        - weight_relation
        """
        self.weight_grammar = weight_grammar
        # Removed multi-task weight updates:
        # self.weight_subdomain = weight_subdomain
        # self.weight_position = weight_position
        # self.weight_entity_type = weight_entity_type
        # self.weight_relation = weight_relation
        print(f"Updated loss weights - Grammar: {weight_grammar}")
        # Removed multi-task weight logging:
        # print(f"Updated loss weights - Grammar: {weight_grammar}, Subdomain: {weight_subdomain}, "
        #       f"Position: {weight_position}, Entity: {weight_entity_type}, Relation: {weight_relation}")

    def train_one_stage(self, tokenizer, train_dataset, eval_dataset, epochs, stage_name,
                       max_row_count=None, save_intermediate=True, learning_rate=2e-5):
        """Train model for one stage with specific configuration"""

        if max_row_count is None:
            max_row_count = len(train_dataset)

        print(f"\n{'='*60}")
        print(f"Starting {stage_name}")
        print(f"{'='*60}")
        print(f"Training for {epochs} epochs with {max_row_count} samples")
        print(f"Current loss weights - Grammar: {self.weight_grammar}")
        # Removed multi-task weight logging:
        # print(f"Current loss weights - Grammar: {self.weight_grammar}, Subdomain: {self.weight_subdomain}, "
        #       f"Position: {self.weight_position}, Entity: {self.weight_entity_type}, Relation: {self.weight_relation}")

        # Preprocess datasets
        tokenized_train_dataset = train_dataset.map(
            lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer),
            batched=True
        )
        tokenized_train_dataset.set_format(type='torch')

        if eval_dataset is not None:
            tokenized_eval_dataset = eval_dataset.map(
                lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer),
                batched=True
            )
            tokenized_eval_dataset.set_format(type='torch')
        else:
            tokenized_eval_dataset = None

        # Configure training arguments for this stage
        output_dir = f"./results/{stage_name.lower().replace(' ', '_')}"
        training_args = TrainingArguments(
            max_steps=max_row_count * epochs,
            output_dir=output_dir,
            overwrite_output_dir=True,
            save_strategy="epoch" if save_intermediate else "no",
            save_steps=max_row_count if save_intermediate else 1000,
            save_total_limit=2,
            eval_strategy="epoch" if eval_dataset else "no",
            per_device_train_batch_size=1,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=4,
            num_train_epochs=1,  # We control epochs via max_steps
            logging_dir=f'./logs/{stage_name.lower().replace(" ", "_")}',
            logging_steps=50,
            warmup_ratio=0.01,
            lr_scheduler_type="cosine",
            learning_rate=learning_rate,
            report_to="tensorboard"
        )

        trainer = Trainer(
            model=self,
            args=training_args,
            train_dataset=tokenized_train_dataset,
            eval_dataset=tokenized_eval_dataset if tokenized_eval_dataset else None,
            processing_class=tokenizer,
            compute_metrics=RoBERTaForConversationClassification.compute_metrics if tokenized_eval_dataset else None
        )

        # Train for this stage
        train_result = trainer.train()

        # Save intermediate checkpoint if requested
        if save_intermediate:
            checkpoint_path = f"./my_models/{stage_name.lower().replace(' ', '_')}_checkpoint"
            self.save_pretrained(checkpoint_path)
            tokenizer.save_pretrained(checkpoint_path)
            print(f"Saved intermediate checkpoint to {checkpoint_path}")

        print(f"Completed {stage_name}")
        return train_result

    def staged_training(self, tokenizer, train_dataset, eval_dataset, custom_epoch=5,
                       save_intermediate=True, resume_from_stage=1):
        """
        Train the model using a three-stage progressive approach

        Args:
            tokenizer: The tokenizer to use
            train_dataset: Training dataset
            eval_dataset: Evaluation dataset
            custom_epoch: Total epochs to distribute across stages (default: 5)
            save_intermediate: Whether to save checkpoints after each stage
            resume_from_stage: Stage to resume from (1, 2, or 3)
        """

        if train_dataset is None or eval_dataset is None:
            raise ValueError("Both train_dataset and eval_dataset must be provided for staged training")

        max_row_count = len(train_dataset)
        print(f"\n{'='*80}")
        print(f"STARTING STAGED TRAINING")
        print(f"{'='*80}")
        print(f"Total training samples: {max_row_count}")
        print(f"Total evaluation samples: {len(eval_dataset)}")
        print(f"Total epochs to distribute: {custom_epoch}")
        print(f"Resume from stage: {resume_from_stage}")
        print(f"Save intermediate checkpoints: {save_intermediate}")

        # Store original weights for restoration if needed (simplified for grammar-only)
        original_weights = {
            'grammar': self.weight_grammar
            # Removed multi-task weights:
            # 'subdomain': self.weight_subdomain,
            # 'position': self.weight_position,
            # 'entity_type': self.weight_entity_type,
            # 'relation': self.weight_relation
        }

        stage_results = {}

        try:
            # Stage 1: Grammar-only training (simplified from multi-task foundation)
            if resume_from_stage <= 1:
                print(f"\n🚀 Preparing Stage 1: Grammar Classification Training")
                self.update_loss_weights(weight_grammar=1.0)
                # Removed multi-task weight configuration:
                # self.update_loss_weights(
                #     weight_grammar=2.0,
                #     weight_subdomain=1.0,
                #     weight_position=3.0,
                #     weight_entity_type=1.0,
                #     weight_relation=0.1
                # )

                stage1_epochs = max(1, custom_epoch)  # Use all epochs for grammar training
                stage_results['stage1'] = self.train_one_stage(
                    tokenizer=tokenizer,
                    train_dataset=train_dataset,
                    eval_dataset=eval_dataset,
                    epochs=stage1_epochs,
                    stage_name="Stage 1 Grammar Classification",
                    max_row_count=max_row_count,
                    save_intermediate=save_intermediate,
                    learning_rate=2e-5  # Standard LR for grammar classification
                )

            # Removed Stage 2 and Stage 3 as they are not needed for grammar-only model:
            # Stage 2: Entity Types - Introduce entity type classification
            # Stage 3: Relations - Full multi-task learning with relation emphasis

        except Exception as e:
            print(f"\n❌ Error during staged training: {e}")
            print("Restoring original loss weights...")
            self.update_loss_weights(original_weights['grammar'])
            # Removed multi-task weight restoration:
            # self.update_loss_weights(**original_weights)
            raise e

        # Final model save
        final_model_path = "./my_models/staged_training_final"
        self.save_pretrained(final_model_path)
        tokenizer.save_pretrained(final_model_path)

        print(f"\n{'='*80}")
        print(f"STAGED TRAINING COMPLETED SUCCESSFULLY!")
        print(f"{'='*80}")
        print(f"Final model saved to: {final_model_path}")
        print(f"Training summary:")
        for stage, result in stage_results.items():
            print(f"  - {stage}: Completed successfully")

        # Automatically generate training visualization
        try:
            print(f"\n📊 Generating training visualization...")
            from loss_visualization import visualize_training_loss

            viz_results = visualize_training_loss(
                logs_dir="./logs",
                output_dir="./visualizations",
                run_name="staged_training",
                show_plots=False,  # Don't show during training
                save_plots=True
            )

            if viz_results:
                print(f"✅ Training visualization saved to ./visualizations/")
                if viz_results['issues']:
                    print(f"⚠️ Detected {len(viz_results['issues'])} training issues")
                if viz_results['recommendations']:
                    print(f"💡 Generated {len(viz_results['recommendations'])} recommendations")

        except Exception as e:
            print(f"⚠️ Could not generate visualization: {e}")

        return stage_results
    
    @staticmethod
    def load_labels_fromfile(filepath):

        labelmap = {}
        json_file = open(filepath, "r", encoding="utf-8")
        json_data = json.load(json_file)
        for idx, item in enumerate(json_data):
            label = item["label"]
            # 修复：标签ID从1开始，以匹配preprocess_dataset中的grammar_id-1逻辑
            labelmap[label] = idx + 1
        json_file.close()

        idary = list(labelmap.keys())

        return labelmap, idary
        
    @staticmethod
    def cvt_labsjson_to_trainjson(labsjson):

        train_json = []
        for item in labsjson:

            # get sentence
            sentence = item["data"]["text"]
            if not sentence:
                print("Warning: Sentence is empty, skipping this item.")
                continue

            # get grammar labels, from_name is "style" and type is "choices"
            grammar_labels = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "choices":
                        continue
                    # label studio intent label name, can change before annotation
                    if result["from_name"] != "style":
                        continue
                    if not "value" in result or not "choices" in result["value"] or len(result["value"]["choices"]) == 0:
                        print(f"Warning: {sentence} Grammar label has no choices.")
                        continue
                    # build train label
                    grammar_labels = result["value"]["choices"]
                    break
            if len(grammar_labels) == 0:
                print(f"Warning: {sentence} Grammar label is empty, using default 'unknown'.")
                grammar_labels = ["unknown"]

            # get subdomain label, from_name is "subdomain" and type is "choices"
            subdomain_label = None
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "choices":
                        continue
                    # label studio intent label name, can change before annotation
                    if result["from_name"] != "subdomain":
                        continue
                    if not "value" in result or not "choices" in result["value"] or len(result["value"]["choices"]) == 0:
                        print(f"Warning: {sentence} Subdomain label has no choices.")
                        continue
                    # build train label
                    subdomain_label = result["value"]["choices"][0]
                    break
            if subdomain_label is None:
                print(f"Warning: {sentence} Subdomain label is empty, using default 'unknown'.")
                subdomain_label = "unknown"

            # get entity spans, from_name is "label" and type is "labels"
            entity_spans = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "labels":
                        continue
                    # label studio entity label name, can change before annotation
                    if result["from_name"] != "label":
                        continue
                    if not "value" in result or not "start" in result["value"] or not "end" in result["value"]:
                        print(f"Warning: {sentence} Entity span has no start or end.")
                        continue
                    if not "labels" in result["value"] or len(result["value"]["labels"]) == 0:
                        print(f"Warning: {sentence} Entity span has no label.")
                        continue
                    # build train label
                    span = {
                        "id": result["id"],
                        "start": result["value"]["start"],
                        "end": result["value"]["end"],
                        "type": result["value"]["labels"][0]
                    }
                    entity_spans.append(span)
            # get entity relations, from_id is not empty and type is "relation"
            entity_relations = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "relation":
                        continue
                    # search entity spans match id find the start and end index
                    from_entity = next((e for e in entity_spans if e["id"] == result["from_id"]), None)
                    to_entity = next((e for e in entity_spans if e["id"] == result["to_id"]), None)
                    if from_entity is None or to_entity is None:
                        print(f"Warning: {sentence} Entity relation has invalid entity references.")
                        continue
                    if not "labels" in result or len(result["labels"]) == 0:
                        print(f"Warning: {sentence} Entity relation has no label.")
                        continue
                    # build train label
                    relation = {
                        "start_i": from_entity["start"],
                        "end_i": from_entity["end"],
                        "start_j": to_entity["start"],
                        "end_j": to_entity["end"],
                        "type": result["labels"][0]
                    }
                    entity_relations.append(relation)
            
            # remove entity spans id column after used in entity_relations
            entity_spans = [{"start": e["start"], "end": e["end"], "type": e["type"]} for e in entity_spans]

            # append to train json
            train_json.append({
                "sentence": sentence,
                "grammar_labels": grammar_labels,
                "subdomain_label": subdomain_label,
                "entity_spans": entity_spans,
                "relation_pairs": entity_relations
            })

        return train_json

def main(my_dataset=None, my_train_dataset=None, my_eval_dataset=None, my_model=None, my_tokenizer=None):

    return_code = -1

    options = [
        "1. Load training dataset",
        "2. Concat additional datasets",
        "3. Load pre-trained model",
        "4. Train model with loaded datasets",
        "5. Run inference with test data",
        "6. Convert LabelStudio export to training format",
        "7. Save model to my_models directory",
        "8. Train model with staged approach",
        "9. Visualize training loss",
        "0. Exit"
    ]

    # if my_model is not None then display the model path behind the option
    if my_model is not None:
        options[2] = f"3. Load pre-trained model (model: {my_model.name_or_path})"
    
    # if my_train_dataset is not None and my_eval_dataset is not None, display the dataset size behind the option
    if my_train_dataset is not None and my_eval_dataset is not None:
        options[0] = f"1. Load training dataset (train size: {len(my_train_dataset)}, eval size: {len(my_eval_dataset)})"
        options[1] = f"2. Concat additional datasets (train size: {len(my_train_dataset)}, eval size: {len(my_eval_dataset)})"

    terminal_menu = TerminalMenu(options, title="=== Conversation Model Training Interface ===")
    choice_index = terminal_menu.show()
    choice = str(options[choice_index][0]) if choice_index is not None else "0"

    if choice == "0":
        return_code = 0
        print("Exiting program.")
        return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
    
    # load or concat additional datasets
    elif choice == "1" or choice == "2":

        # clear previous dataset if is loading new dataset
        if choice == "1":
            my_train_dataset = None
            my_eval_dataset = None

        # List all files in the datasets directory
        dataset_files = [f for f in os.listdir("./datasets/") if f.endswith(".json")]
    
        # use terminal menu to select a dataset
        if not dataset_files:
            print("No JSON dataset files found in ./datasets/ directory.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        dataset_files.sort()
        dataset_menu = [f"./datasets/{f}" for f in dataset_files]
        dataset_menu = [f"{i+1}. {f}" for i, f in enumerate(dataset_menu)]
        dataset_menu = [f"0. Clear dataset loaded"] + dataset_menu

        terminal_menu = TerminalMenu(dataset_menu, title="=== Available datasets ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            # Extract the number before the period in the menu item
            choice = int(dataset_menu[choice_index].split('.')[0])
        else:
            choice = 0
        
        try:

            if 0 <= choice < len(dataset_files):
            
                dataset_path = dataset_files[choice-1]
                print(f"Loading {dataset_path}...")
            
                additional_dataset = load_dataset("json", data_files=f"./datasets/{dataset_path}")
                additional_split = additional_dataset["train"].train_test_split(test_size=0.1)
            
                if my_train_dataset is not None and my_eval_dataset is not None:
                    my_train_dataset = concatenate_datasets([my_train_dataset, additional_split["train"]])
                    my_eval_dataset = concatenate_datasets([my_eval_dataset, additional_split["test"]])
                    my_train_dataset = my_train_dataset.shuffle(seed=42)
                    my_eval_dataset = my_eval_dataset.shuffle(seed=42)
                    print(f"Datasets combined. New sizes - Training: {len(my_train_dataset)}, Evaluation: {len(my_eval_dataset)}")
                else:
                    my_train_dataset = additional_split["train"]
                    my_eval_dataset = additional_split["test"]
                    print(f"Dataset loaded. Training samples: {len(my_train_dataset)}, Evaluation samples: {len(my_eval_dataset)}")

                print("dataset columns: ", my_train_dataset.column_names)
                
            elif choice != 0:
                print("Invalid selection.")
            elif choice == 0:
                print("Clearing loaded datasets.")
                my_train_dataset = None
                my_eval_dataset = None
        except ValueError:
            print("Please enter a valid number.") 
        except Exception as e:
            print(f"Error loading dataset: {e}")
            print("Please try again.")

    # Load pre-trained model
    elif choice == "3":

        # List available model directories
        available_models = []
        # Find the deepest level directories in dl_models
        if os.path.exists("./dl_models/"):
            for root, dirs, files in os.walk("./dl_models/"):
                # If this is a leaf directory (no subdirectories)
                if not dirs:
                    available_models.append(root)            

        if os.path.exists("./my_models/"):
            my_models = [f"./my_models/{d}" for d in os.listdir("./my_models/") if os.path.isdir(f"./my_models/{d}")]
            available_models.extend(my_models)
        
        if not available_models:
            print("No available models found in ./dl_models/ or ./my_models/ directories.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        available_models.sort()
        models_menu = [f"{i+1}. {f}" for i, f in enumerate(available_models)]
        models_menu = [f"0. Cancel"] + models_menu

        terminal_menu = TerminalMenu(models_menu, title="=== Available models ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            choice = int(models_menu[choice_index].split('.')[0])
        else:
            choice = 0
        
        if 0 <= choice-1 < len(available_models):
            model_path = available_models[choice-1]
        else:
            print("Invalid selection.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        print(f"Loading model from {model_path}...")
        try:
            my_model = RoBERTaForConversationClassification.from_pretrained(model_path)
            my_tokenizer = BertTokenizerFast.from_pretrained(model_path)
            print(f"Model loaded successfully from {model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")

    # Train model with loaded datasets
    elif choice == "4":

        if my_train_dataset is None or my_eval_dataset is None:
            print("No datasets loaded. Please load datasets first (options 1-2).")
        elif 'my_model' in locals() and 'my_tokenizer' in locals():

            # Create menu for epoch selection
            epoch_options = ["1 epoch", "3 epochs", "5 epochs", "10 epochs", "Custom..."]
            epoch_menu = TerminalMenu(epoch_options, title="Select number of training epochs:")
            epoch_idx = epoch_menu.show()
            
            if epoch_idx == 4:  # Custom option
                epochs_input = input("Enter custom number of epochs: ").strip()
                epochs_input = epochs_input.replace("\r", "").replace("\n", "")
                epochs = int(epochs_input or "3")
            else:
                # Extract number from selected option
                epochs = int(epoch_options[epoch_idx].split()[0]) 

            # Create menu for save steps selection
            save_steps_options = ["100 steps", "500 steps", "1000 steps", "5000 steps", "Custom..."]
            save_steps_menu = TerminalMenu(save_steps_options, title="Select save frequency:")
            save_steps_idx = save_steps_menu.show()
            
            if save_steps_idx == 4:  # Custom option
                save_steps_input = input("Enter custom save steps: ").strip()
                save_steps_input = save_steps_input.replace("\r", "").replace("\n", "")
                save_steps = int(save_steps_input or "100")
            else:
                # Extract number from selected option
                save_steps = int(save_steps_options[save_steps_idx].split()[0]) 

            print(f"Training model with {len(my_train_dataset)} samples for {epochs} epochs...")
            
            try:
                my_model.custom_train(my_tokenizer, my_train_dataset, my_eval_dataset, max_row_count=None, 
                                  custom_epoch=epochs, save_steps=save_steps)
            except Exception as e:
                print(f"Error during training: {e}")
    
    # Run inference with test data
    elif choice == "5":
        if 'my_model' not in locals() or 'my_tokenizer' not in locals():
            print("No model loaded. Please load a model first (option 3).")
        else:
            # load inference sentence from file or custom input
            if not os.path.exists("./datasets/my_roberta_v2_testdata.txt"):
                print("No test data file found at ./datasets/my_roberta_v2_testdata.txt. Please create this file with test sentences.")
                return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
            # read default sentences from file
            with open("./datasets/my_roberta_v2_testdata.txt", "r", encoding="utf-8") as f:
                # read lines, delete empty lines and strip whitespace
                default_sentences = f.readlines()
                default_sentences = [s.strip() for s in default_sentences if s.strip()]
                if not default_sentences:
                    print("No valid sentences found in ./datasets/my_roberta_v2_testdata.txt. Please add some sentences.")
                    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
            
            # display sentence select menu or custom input
            option_menu = [f"{i+1}. {f}" for i, f in enumerate(default_sentences)]
            option_menu = [f"0. Custom input, Empty means quit"] + option_menu
            terminal_menu = TerminalMenu(option_menu, title="=== Available sentences ===")
            choice_index = terminal_menu.show()
            if choice_index is not None:
                choice = int(option_menu[choice_index].split('.')[0])
            else:
                choice = 0
            if choice == 0:
                test_sentence = input("Enter test sentence (default: deflist): ")
                if not test_sentence:
                    print("No sentence entered. Exiting.")
                    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
                else:
                    test_sentence = [test_sentence]
            else:
                test_sentence = [default_sentences[choice-1]]
                print(f"Selected sentence: {test_sentence[0]}")

            try:
                print(f"Running inference on {test_sentence}...")
                inference_output = my_model.inference(test_sentence, my_tokenizer)
                for i, entry in enumerate(inference_output):
                    print(f"\n--- Result {i+1} ---")
                    print(f"Sentence: {entry['sentence']}")
                    print(f"Grammar Labels: {entry['grammar_labels']}")
                    # Removed multi-task output display:
                    # print(f"Subdomain: {entry['subdomain']}")
                    # print("Entities:")
                    # for entity in entry['entities']:
                    #     print(f"  - [{entity['start']:02d}:{entity['end']:02d}] ({entity['type']}) {entity['value']}")
                    # print("Relations:")
                    # for relation in entry['relations']:
                    #     print(f"  - [{relation['entity1']['start']:02d}:{relation['entity1']['end']:02d}] ({relation['entity1']['type']}) ({relation['entity1']['value']})"
                    #           f" ={relation['relation_type']}= "
                    #           f"[{relation['entity2']['start']:02d}:{relation['entity2']['end']:02d}] ({relation['entity2']['type']}) ({relation['entity2']['value']})")
            except Exception as e:
                print(f"Error during inference: {e}")

    elif choice == "6":
        # display all the json files in datasets directory
        labsjson_files = [f for f in os.listdir("./datasets/") if f.endswith(".json")]
        # display menu to select a file
        if not labsjson_files:
            print("No LabelStudio export files found in ./datasets/ directory.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        labsjson_files.sort()
        labsjson_menu = [f"{i+1}. {f}" for i, f in enumerate(labsjson_files)]
        labsjson_menu = [f"0. Cancel"] + labsjson_menu
        terminal_menu = TerminalMenu(labsjson_menu, title="=== Available LabelStudio export files ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            choice = int(labsjson_menu[choice_index].split('.')[0])
        else:
            choice = 0
        if choice == 0:
            print("Cancelled conversion.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        input_file = "./datasets/" + labsjson_files[choice-1]

        # display menu to select output file    
        option = [
            "./datasets/my_roberta_v2_traindata.json",
            "./datasets/my_roberta_v2_traindata_error.json",
            # "./datasets/my_roberta_v2_traindata_test_0630_1645.json"
            # "./datasets/my_roberta_v2_traindata_test_0702_1101.json"

        ]
        option_menu = [f"{i+1}. {f}" for i, f in enumerate(option)]
        option_menu = [f"0. Custom output file"] + option_menu
        terminal_menu = TerminalMenu(option_menu, title="=== Select output file ===")
        choice_index = terminal_menu.show()
        if choice_index == 0:
            output_file = input("Enter output file path: ").strip()
            if not output_file or not output_file.endswith(".json"): # empty output file means exit or not end with .json
                print("Invalid output file path. Exiting conversion.")
                return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        elif choice_index == 1 or choice_index == 2:
            output_file = option[choice_index-1]
        else:
            print("Invalid selection.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer

        try:
            with open(input_file, "r", encoding="utf-8") as f:
                labsjson = json.load(f)
            trainjson = RoBERTaForConversationClassification.cvt_labsjson_to_trainjson(labsjson)
            # if output file does not exist, create it
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            # write trainjson to output file
            print(f"Writing converted data to {output_file}...")
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(trainjson, f, ensure_ascii=False, indent=2)
            print(f"Converted {len(trainjson)} samples and saved to {output_file}")
        except Exception as e:
            print(f"Error in conversion: {e}")

    elif choice == "7":
        # Save model to my_models directory
        if my_model is None or my_tokenizer is None:
            print("No model loaded. Please load a model first (option 3).")
        else:
            # Create my_models directory if not exists
            if not os.path.exists("./my_models/"):
                os.makedirs("./my_models/")
            # Save model and tokenizer
            try:
                save_path = f"./my_models/my_roberta_V2"
                my_model.save_pretrained(save_path)
                my_tokenizer.save_pretrained(save_path)
                print(f"Model and tokenizer saved to {save_path}")
            except Exception as e:
                print(f"Error saving model: {e}")

    elif choice == "8":
        # Train model with staged approach
        if my_train_dataset is None or my_eval_dataset is None:
            print("No datasets loaded. Please load datasets first (options 1-2).")
        elif my_model is None or my_tokenizer is None:
            print("No model loaded. Please load a model first (option 3).")
        else:
            try:
                # Create menu for total epochs selection
                epoch_options = ["3 epochs", "5 epochs", "7 epochs", "10 epochs", "Custom..."]
                epoch_menu = TerminalMenu(epoch_options, title="Select total epochs for staged training:")
                epoch_idx = epoch_menu.show()

                if epoch_idx == 4:  # Custom option
                    epochs_input = input("Enter custom number of total epochs: ").strip()
                    total_epochs = int(epochs_input) if epochs_input.isdigit() else 5
                else:
                    # Extract number from selected option
                    total_epochs = int(epoch_options[epoch_idx].split()[0])

                # Create menu for save intermediate checkpoints
                save_options = ["Save intermediate checkpoints", "No intermediate saves"]
                save_menu = TerminalMenu(save_options, title="Save intermediate checkpoints?")
                save_idx = save_menu.show()
                save_intermediate = (save_idx == 0)

                # Create menu for resume stage
                resume_options = ["Start from Stage 1", "Resume from Stage 2", "Resume from Stage 3"]
                resume_menu = TerminalMenu(resume_options, title="Select starting stage:")
                resume_idx = resume_menu.show()
                resume_from_stage = resume_idx + 1

                print(f"\nStarting staged training with:")
                print(f"  - Total epochs: {total_epochs}")
                print(f"  - Save intermediate: {save_intermediate}")
                print(f"  - Resume from stage: {resume_from_stage}")
                print(f"  - Training samples: {len(my_train_dataset)}")
                print(f"  - Evaluation samples: {len(my_eval_dataset)}")

                # Confirm before starting
                confirm = input("\nProceed with staged training? (y/N): ").strip().lower()
                if confirm == 'y':
                    stage_results = my_model.staged_training(
                        tokenizer=my_tokenizer,
                        train_dataset=my_train_dataset,
                        eval_dataset=my_eval_dataset,
                        custom_epoch=total_epochs,
                        save_intermediate=save_intermediate,
                        resume_from_stage=resume_from_stage
                    )
                    print("\n✅ Staged training completed successfully!")
                    print("Check ./my_models/staged_training_final for the final model.")
                else:
                    print("Staged training cancelled.")

            except Exception as e:
                print(f"Error during staged training: {e}")
                import traceback
                traceback.print_exc()

    elif choice == "9":
        # Visualize training loss
        try:
            from loss_visualization import visualize_training_loss

            # Create menu for visualization options
            viz_options = [
                "Visualize current training logs",
                "Visualize specific log directory",
                "Compare multiple training runs",
                "Generate report only (no plots)"
            ]
            viz_menu = TerminalMenu(viz_options, title="Select visualization option:")
            viz_idx = viz_menu.show()

            if viz_idx == 0:
                # Visualize current logs
                print("\n📊 Analyzing current training logs...")
                results = visualize_training_loss(
                    logs_dir="./logs",
                    output_dir="./visualizations",
                    run_name="current",
                    show_plots=True,
                    save_plots=True
                )

                if results:
                    print(f"\n✅ Analysis complete!")
                    print(f"   Generated {len(results['figures'])} plots")
                    print(f"   Found {len(results['issues'])} issues")
                    print(f"   Generated {len(results['recommendations'])} recommendations")

                    if results['issues']:
                        print("\n⚠️ Issues detected:")
                        for issue in results['issues']:
                            print(f"   - {issue}")

                    if results['recommendations']:
                        print("\n💡 Recommendations:")
                        for rec in results['recommendations']:
                            print(f"   - {rec}")
                else:
                    print("❌ No training logs found to analyze")

            elif viz_idx == 1:
                # Visualize specific directory
                logs_dir = input("Enter path to logs directory: ").strip()
                if not logs_dir:
                    logs_dir = "./logs"

                if os.path.exists(logs_dir):
                    run_name = input("Enter run name (optional): ").strip()
                    if not run_name:
                        run_name = "custom"

                    results = visualize_training_loss(
                        logs_dir=logs_dir,
                        output_dir="./visualizations",
                        run_name=run_name,
                        show_plots=True,
                        save_plots=True
                    )

                    if results:
                        print(f"\n✅ Analysis complete for {logs_dir}")
                    else:
                        print(f"❌ No valid logs found in {logs_dir}")
                else:
                    print(f"❌ Directory {logs_dir} does not exist")

            elif viz_idx == 2:
                # Compare multiple runs
                print("\n🔄 Setting up training run comparison...")
                print("Enter training run configurations (press Enter with empty name to finish):")

                run_configs = []
                while True:
                    name = input(f"Run {len(run_configs)+1} name: ").strip()
                    if not name:
                        break

                    logs_dir = input(f"Run {len(run_configs)+1} logs directory: ").strip()
                    if not logs_dir:
                        logs_dir = "./logs"

                    if os.path.exists(logs_dir):
                        run_configs.append({'name': name, 'logs_dir': logs_dir})
                        print(f"   Added: {name} -> {logs_dir}")
                    else:
                        print(f"   Skipped: {logs_dir} does not exist")

                if len(run_configs) >= 2:
                    from loss_visualization import compare_training_runs
                    results = compare_training_runs(run_configs, "./visualizations")
                    print(f"\n✅ Comparison complete! Check ./visualizations/")
                else:
                    print("❌ Need at least 2 valid training runs for comparison")

            elif viz_idx == 3:
                # Report only
                results = visualize_training_loss(
                    logs_dir="./logs",
                    output_dir="./visualizations",
                    run_name="report_only",
                    show_plots=False,
                    save_plots=False
                )

                if results and results['report']:
                    print("\n📋 Training Analysis Report:")
                    print("=" * 50)
                    print(results['report'])
                else:
                    print("❌ No training data found for report generation")

        except ImportError:
            print("❌ Loss visualization module not available.")
            print("   Make sure loss_visualization.py is in the current directory.")
        except Exception as e:
            print(f"❌ Error during visualization: {e}")
            import traceback
            traceback.print_exc()

    else:
        print("Invalid option. Please try again.")
    
    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer

if __name__ == "__main__":

    import sys
    import termios
    import tty
    import atexit

    # set terminal to non-canonical mode
    fd = sys.stdin.fileno()
    original_settings = termios.tcgetattr(fd)
    def restore_terminal():
        termios.tcsetattr(fd, termios.TCSADRAIN, original_settings)
        print("\n[Terminal restored]")
    atexit.register(restore_terminal)
    tty.setcbreak(fd)
    # prevent termian abnormal exit

    # Only load grammar labels for simplified grammar-only model
    CONVERSATION_GRAMMAR_ID_MAP, CONVERSATION_ID_GRAMMAR_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_grammar_labels.json")

    # Removed multi-task label loading:
    # CONVERSATION_ENTITY_ID_MAP, CONVERSATION_ID_ENTITY_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_entity_labels.json")
    # CONVERSATION_RELATION_ID_MAP, CONVERSATION_ID_RELATION_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_relation_labels.json")
    # CONVERSATION_SUBDOMAIN_ID_MAP, CONVERSATION_ID_SUBDOMAIN_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_subdomain_labels.json")

    my_dataset = None
    my_train_dataset = None
    my_eval_dataset = None
    my_model = None
    my_tokenizer = None

    while True:
        # Call the main function to start the menu
        return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer = main(my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer)

        if return_code is None or return_code != 0:
            continue
        else:
            break

    print("Program terminated.")