"""
简化的超参数优化器
专注于基础训练参数的优化，适用于custom_train方法
"""

import os
import json
import numpy as np
import torch
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import itertools
import random
import logging

logger = logging.getLogger(__name__)

@dataclass
class SimpleOptimizationConfig:
    """
    简化的优化配置

    target_metric 支持的选项：
    - 'combined_f1': 综合F1分数（默认）
    - 'eval_loss': 验证损失（推荐用于性能较差的模型）
    - 'train_loss': 训练损失
    - 其他F1指标: 'span_f1', 'relation_f1', 'grammar_f1' 等
    - 其他准确率指标: 'subdomain_acc' 等
    """
    search_method: str = 'grid'  # 'grid', 'random'
    max_trials: int = 12         # 最大试验次数
    # target_metric: str = 'combined_f1'  # 目标指标
    target_metric: str = 'eval_loss'
    results_dir: str = './simple_optimization_results'
    save_best_model: bool = True  # 是否保存最佳模型

class SimpleHyperparameterOptimizer:
    """简化的超参数优化器"""
    
    def __init__(self, config: SimpleOptimizationConfig):
        self.config = config
        self.results = []
        self.best_score = float('-inf')
        self.best_params = None
        self.trial_count = 0
        
        # 创建结果目录
        os.makedirs(config.results_dir, exist_ok=True)
        
        # 结果文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_file = os.path.join(config.results_dir, f'optimization_results_{timestamp}.json')
    
    def define_search_space(self) -> Dict[str, List]:
        """定义基础训练参数和多任务权重搜索空间"""
        return {
            # 核心训练参数
            'learning_rate': [1e-5, 2e-5, 3e-5, 5e-5],
            'per_device_train_batch_size': [1, 2, 4],
            'gradient_accumulation_steps': [2, 4, 8],
            'custom_epoch': [2, 3, 5],
            'warmup_ratio': [0.01, 0.05, 0.1],
            'weight_decay': [0.0, 0.01, 0.05],
            'lr_scheduler_type': ['linear', 'cosine'],

            # 多任务分类器权重参数
            'weight_grammar': [0.5, 1.0, 1.5, 2.0],        # 语法分类任务权重
            'weight_subdomain': [0.5, 1.0, 1.5, 2.0],      # 子域分类任务权重
            'weight_position': [0.1, 0.5, 1.0, 1.5],       # 位置检测任务权重
            'weight_entity_type': [0.5, 1.0, 1.5, 2.0],    # 实体类型分类任务权重
            'weight_relation': [0.5, 1.0, 1.5, 2.0],       # 关系抽取任务权重

            # 评估和保存策略
            'eval_steps_ratio': [0.25, 0.5],  # eval_steps = max_row_count * ratio
            'save_steps': [100, 500]
        }
    
    def generate_parameter_combinations(self, search_space: Dict[str, List]) -> List[Dict]:
        """生成参数组合"""
        if self.config.search_method == 'grid':
            # 网格搜索：生成所有组合，然后限制数量
            all_combinations = []
            keys = list(search_space.keys())
            values = list(search_space.values())
            
            for combination in itertools.product(*values):
                param_dict = dict(zip(keys, combination))
                all_combinations.append(param_dict)
            
            # 如果组合太多，随机选择一部分
            if len(all_combinations) > self.config.max_trials:
                random.shuffle(all_combinations)
                all_combinations = all_combinations[:self.config.max_trials]
            
            return all_combinations
        
        elif self.config.search_method == 'random':
            # 随机搜索
            combinations = []
            for _ in range(self.config.max_trials):
                combo = {}
                for param, values in search_space.items():
                    combo[param] = random.choice(values)
                combinations.append(combo)
            return combinations
        
        else:
            raise ValueError(f"Unsupported search method: {self.config.search_method}")
    
    def evaluate_parameters(self, params: Dict, model, tokenizer, train_dataset, eval_dataset) -> Dict:
        """评估单组参数的性能"""
        self.trial_count += 1
        logger.info(f"Trial {self.trial_count}/{self.config.max_trials}")
        logger.info(f"Testing parameters: {params}")

        try:
            # 执行训练
            results = self._train_with_params(params, model, tokenizer, train_dataset, eval_dataset)

            # 计算评估分数
            score = self._calculate_score(results)

            trial_result = {
                'trial': self.trial_count,
                'parameters': params,
                'training_results': results,
                'score': score,
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            }

            self.results.append(trial_result)

            # 更新最佳结果
            if score > self.best_score:
                self.best_score = score
                self.best_params = params.copy()
                logger.info(f"🎉 New best score: {score:.4f}")

                # 保存最佳模型
                if self.config.save_best_model:
                    try:
                        self._save_best_model(model, tokenizer, params, score)
                    except Exception as save_error:
                        logger.warning(f"Failed to save best model: {save_error}")

            # 保存中间结果
            self._save_intermediate_results()

            logger.info(f"✅ Trial {self.trial_count} completed successfully with score: {score:.4f}")
            return trial_result

        except KeyboardInterrupt:
            logger.info("❌ Trial interrupted by user")
            raise
        except Exception as e:
            logger.error(f"❌ Trial {self.trial_count} failed: {str(e)}")
            import traceback
            logger.error(f"Full error traceback: {traceback.format_exc()}")

            trial_result = {
                'trial': self.trial_count,
                'parameters': params,
                'error': str(e),
                'score': float('-inf'),
                'timestamp': datetime.now().isoformat(),
                'status': 'failed'
            }

            self.results.append(trial_result)
            self._save_intermediate_results()

            return trial_result
    
    def _train_with_params(self, params: Dict, model, tokenizer, train_dataset, eval_dataset) -> Dict:
        """使用指定参数训练模型"""
        from transformers import Trainer, TrainingArguments

        # 应用多任务权重到模型
        self._apply_multitask_weights(model, params)

        # 定义预处理函数，避免lambda序列化问题
        def preprocess_function(examples):
            # 使用类的静态方法正确调用preprocess_dataset
            return model.__class__.preprocess_dataset(
                examples,
                tokenizer,
                max_length=256,
                padding="max_length",
                truncation=True
            )

        # 预处理数据集
        try:
            tokenized_train = train_dataset.map(
                preprocess_function,
                batched=True,
                remove_columns=train_dataset.column_names,
                desc="Preprocessing train dataset"
            )
            tokenized_train.set_format(type='torch')

            tokenized_eval = eval_dataset.map(
                preprocess_function,
                batched=True,
                remove_columns=eval_dataset.column_names,
                desc="Preprocessing eval dataset"
            )
            tokenized_eval.set_format(type='torch')

        except Exception as e:
            logger.error(f"Dataset preprocessing failed: {e}")
            raise
        
        # 计算训练步数
        max_row_count = len(train_dataset)
        custom_epoch = params.get('custom_epoch', 3)
        eval_steps_ratio = params.get('eval_steps_ratio', 0.5)
        save_steps = params.get('save_steps', 500)
        
        # 配置训练参数
        training_args = TrainingArguments(
            max_steps=max_row_count * custom_epoch,
            output_dir=f"{self.config.results_dir}/trial_{self.trial_count}",
            overwrite_output_dir=True,
            save_strategy="no",  # 不保存中间检查点以节省空间
            eval_strategy="steps",
            eval_steps=int(max_row_count * eval_steps_ratio),
            per_device_train_batch_size=params.get('per_device_train_batch_size', 1),
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=params.get('gradient_accumulation_steps', 4),
            num_train_epochs=1,  # 通过max_steps控制
            logging_dir=f"{self.config.results_dir}/trial_{self.trial_count}/logs",
            logging_steps=100,
            warmup_ratio=params.get('warmup_ratio', 0.01),
            lr_scheduler_type=params.get('lr_scheduler_type', 'cosine'),
            learning_rate=params.get('learning_rate', 2e-5),
            weight_decay=params.get('weight_decay', 0.0),
            report_to=None,  # 禁用tensorboard以节省资源
            dataloader_pin_memory=False,
            remove_unused_columns=False
        )
        
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=tokenized_train,
            eval_dataset=tokenized_eval,
            processing_class=tokenizer,
            compute_metrics=model.compute_metrics
        )
        
        # 执行训练
        train_result = trainer.train()
        
        # 最终评估
        eval_result = trainer.evaluate()
        
        return {
            'train_loss': train_result.training_loss,
            'eval_metrics': eval_result
        }
    
    def _calculate_score(self, results: Dict) -> float:
        """计算评估分数"""
        eval_metrics = results.get('eval_metrics', {})

        if self.config.target_metric == 'combined_f1':
            # 综合F1分数
            span_f1 = eval_metrics.get('eval_span_f1', 0.0)
            relation_f1 = eval_metrics.get('eval_relation_f1', 0.0)
            grammar_f1 = eval_metrics.get('eval_grammar_f1', 0.0)
            subdomain_acc = eval_metrics.get('eval_subdomain_acc', 0.0)
            start_end_f1 = eval_metrics.get('eval_start_end_f1', 0.0)

            # 加权综合分数
            combined_score = (
                span_f1 * 0.3 +           # 实体识别
                relation_f1 * 0.3 +       # 关系抽取
                grammar_f1 * 0.2 +        # 语法分类
                subdomain_acc * 0.1 +     # 子域分类
                start_end_f1 * 0.1        # 边界检测
            )
            return combined_score

        elif self.config.target_metric == 'eval_loss':
            # 验证损失优化 - 损失越小越好，所以返回负值
            eval_loss = eval_metrics.get('eval_loss', float('inf'))
            if eval_loss == float('inf'):
                logger.warning("eval_loss not found in eval_metrics, returning very low score")
                return float('-inf')

            # 返回负损失值，这样优化器会寻找最大值（即最小损失）
            return -eval_loss

        elif self.config.target_metric == 'train_loss':
            # 训练损失优化 - 损失越小越好，所以返回负值
            train_loss = results.get('train_loss', float('inf'))
            if train_loss == float('inf'):
                logger.warning("train_loss not found in results, returning very low score")
                return float('-inf')

            # 返回负损失值
            return -train_loss

        elif self.config.target_metric.endswith('_loss'):
            # 通用损失指标处理
            loss_value = eval_metrics.get(self.config.target_metric, float('inf'))
            if loss_value == float('inf'):
                logger.warning(f"{self.config.target_metric} not found in eval_metrics, returning very low score")
                return float('-inf')
            return -loss_value

        else:
            # 其他指标（F1、准确率等）- 越大越好
            return eval_metrics.get(f'eval_{self.config.target_metric}', 0.0)

    def _apply_multitask_weights(self, model, params: Dict):
        """应用多任务权重到模型"""
        # 提取多任务权重参数，使用默认值如果参数不存在
        weight_grammar = params.get('weight_grammar', 1.0)
        weight_subdomain = params.get('weight_subdomain', 1.0)
        weight_position = params.get('weight_position', 1.0)
        weight_entity_type = params.get('weight_entity_type', 1.0)
        weight_relation = params.get('weight_relation', 1.0)

        # 应用权重到模型
        if hasattr(model, 'update_loss_weights'):
            model.update_loss_weights(
                weight_grammar=weight_grammar,
                weight_subdomain=weight_subdomain,
                weight_position=weight_position,
                weight_entity_type=weight_entity_type,
                weight_relation=weight_relation
            )
            logger.info(f"Applied multitask weights - Grammar: {weight_grammar}, Subdomain: {weight_subdomain}, "
                       f"Position: {weight_position}, Entity: {weight_entity_type}, Relation: {weight_relation}")
        else:
            logger.warning("Model does not have update_loss_weights method, skipping weight application")

    def _save_best_model(self, model, tokenizer, params: Dict, score: float):
        """保存最佳模型"""
        try:
            model_name = f"best_model_score_{score:.4f}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            model_path = os.path.join(self.config.results_dir, model_name)
            os.makedirs(model_path, exist_ok=True)
            
            # 保存模型和tokenizer
            model.save_pretrained(model_path)
            tokenizer.save_pretrained(model_path)
            
            # 保存参数配置
            config_file = os.path.join(model_path, 'optimization_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'best_parameters': params,
                    'best_score': score,
                    'optimization_config': self.config.__dict__
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Best model saved to: {model_path}")
            
        except Exception as e:
            logger.error(f"Failed to save best model: {e}")
    
    def _save_intermediate_results(self):
        """保存中间结果"""
        with open(self.results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'config': self.config.__dict__,
                'best_score': self.best_score,
                'best_params': self.best_params,
                'results': self.results,
                'total_trials': self.trial_count
            }, f, indent=2, ensure_ascii=False)
    
    def optimize(self, model, tokenizer, train_dataset, eval_dataset) -> Dict:
        """执行超参数优化"""
        logger.info(f"Starting simple hyperparameter optimization")
        logger.info(f"Search method: {self.config.search_method}")
        logger.info(f"Max trials: {self.config.max_trials}")
        logger.info(f"Target metric: {self.config.target_metric}")
        
        # 定义搜索空间
        search_space = self.define_search_space()
        
        # 生成参数组合
        param_combinations = self.generate_parameter_combinations(search_space)
        logger.info(f"Generated {len(param_combinations)} parameter combinations")
        
        # 执行优化
        successful_trials = 0
        failed_trials = 0

        for i, params in enumerate(param_combinations):
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"Starting trial {i+1}/{len(param_combinations)}")
                logger.info(f"Progress: {((i+1)/len(param_combinations)*100):.1f}%")
                logger.info(f"{'='*60}")

                # 评估当前参数组合
                result = self.evaluate_parameters(params, model, tokenizer, train_dataset, eval_dataset)

                # 统计结果
                if result.get('status') == 'success':
                    successful_trials += 1
                    logger.info(f"✅ Trial {i+1} completed successfully. Score: {result.get('score', 'N/A'):.4f}")
                else:
                    failed_trials += 1
                    logger.info(f"❌ Trial {i+1} failed. Error: {result.get('error', 'Unknown error')}")

                logger.info(f"📊 Progress: {successful_trials} successful, {failed_trials} failed")
                logger.info(f"🏆 Current best score: {self.best_score:.4f}")

            except KeyboardInterrupt:
                logger.info(f"\n⚠️ Optimization interrupted by user after {i+1} trials")
                logger.info(f"📊 Final stats: {successful_trials} successful, {failed_trials} failed")
                break
            except Exception as e:
                logger.error(f"❌ Unexpected error in trial {i+1}: {e}")
                failed_trials += 1
                continue
        
        # 保存最终结果
        final_results = {
            'optimization_config': self.config.__dict__,
            'best_score': self.best_score,
            'best_parameters': self.best_params,
            'total_trials': self.trial_count,
            'all_results': self.results,
            'search_space': search_space
        }
        
        final_results_file = os.path.join(self.config.results_dir, 'final_optimization_results.json')
        with open(final_results_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        # 显示最终结果
        logger.info(f"\n{'='*80}")
        logger.info(f"OPTIMIZATION COMPLETED!")
        logger.info(f"{'='*80}")
        logger.info(f"📊 Total trials: {self.trial_count}")
        logger.info(f"✅ Successful trials: {successful_trials}")
        logger.info(f"❌ Failed trials: {failed_trials}")

        if self.best_score > float('-inf'):
            logger.info(f"🏆 Best score: {self.best_score:.4f}")
            logger.info(f"🎯 Best parameters:")
            for key, value in (self.best_params or {}).items():
                if isinstance(value, float):
                    logger.info(f"   - {key}: {value:.6f}")
                else:
                    logger.info(f"   - {key}: {value}")
        else:
            logger.warning(f"⚠️ No successful trials! All {self.trial_count} trials failed.")
            logger.warning(f"Please check your data and model configuration.")

        logger.info(f"📁 Results saved to: {final_results_file}")

        return final_results
    
    def get_best_parameters_for_custom_train(self) -> Dict:
        """获取适用于custom_train方法的最佳参数"""
        if not self.best_params:
            return {}

        # 转换为custom_train方法需要的参数格式
        custom_train_params = {
            # 基础训练参数
            'custom_epoch': self.best_params.get('custom_epoch', 3),
            'save_steps': self.best_params.get('save_steps', 500),
            'learning_rate': self.best_params.get('learning_rate', 2e-5),
            'per_device_train_batch_size': self.best_params.get('per_device_train_batch_size', 1),
            'gradient_accumulation_steps': self.best_params.get('gradient_accumulation_steps', 4),
            'warmup_ratio': self.best_params.get('warmup_ratio', 0.01),
            'weight_decay': self.best_params.get('weight_decay', 0.0),
            'lr_scheduler_type': self.best_params.get('lr_scheduler_type', 'cosine'),

            # 多任务权重参数
            'weight_grammar': self.best_params.get('weight_grammar', 1.0),
            'weight_subdomain': self.best_params.get('weight_subdomain', 1.0),
            'weight_position': self.best_params.get('weight_position', 1.0),
            'weight_entity_type': self.best_params.get('weight_entity_type', 1.0),
            'weight_relation': self.best_params.get('weight_relation', 1.0)
        }

        return custom_train_params
